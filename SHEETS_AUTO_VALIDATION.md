# 📊 Google Sheets 自動驗證與修正功能

## 🆕 新功能概述

雲林光合菌協會Line BOT系統現在具備了自動掃描和修正Google Sheets工作表結構的功能。系統會自動檢查必要的工作表是否存在，並確保欄位結構正確。

**版本**: v1.1.0  
**新增日期**: 2025-01-24

## ✨ 主要功能

### 🔍 自動掃描功能
- **工作表存在性檢查**: 自動檢查必要的工作表是否存在
- **欄位結構驗證**: 確保每個工作表的標題行正確
- **資料完整性檢查**: 驗證基本資料結構

### 🔧 自動修正功能
- **建立缺失工作表**: 自動建立不存在的必要工作表
- **修正欄位標題**: 自動修正錯誤或缺失的欄位標題
- **新增範例資料**: 為知識庫工作表自動新增基本範例資料
- **格式美化**: 自動設定標題行格式和欄寬

## 📋 必要工作表結構

系統會自動確保以下工作表結構正確：

### 1. 知識庫工作表 (Sheet1)
```
欄位: 關鍵字 | 回答 | 分類
用途: 儲存光合菌專業知識和常見問題回答
範例資料: 包含光合菌、應用、產品等基本資訊
```

### 2. 對話記錄工作表
```
欄位: 時間戳記 | 用戶ID | 事件類型 | 訊息內容 | 完整事件
用途: 記錄所有用戶與BOT的對話內容
```

### 3. Users 工作表
```
欄位: 時間戳記 | 用戶ID | 動作 | 備註
用途: 記錄用戶的關注、取消關注等行為
```

### 4. Errors 工作表
```
欄位: 時間戳記 | 函數名稱 | 錯誤訊息 | 錯誤堆疊 | 版本
用途: 記錄系統運行過程中的錯誤日誌
```

## 🚀 使用方法

### 自動執行 (推薦)

系統會在以下情況自動執行驗證：

1. **系統初始化時**
   ```javascript
   initializeSystem() // 會自動調用工作表驗證
   ```

2. **系統測試時**
   ```javascript
   testSystem() // 包含工作表結構測試
   ```

### 手動執行

您也可以在Google Apps Script編輯器中手動執行：

#### 1. 完整工作表驗證
```javascript
manualValidateSheets()
```
**功能**: 檢查並修正所有工作表結構

#### 2. 僅驗證不修正
```javascript
validateAndFixSheets()
```
**功能**: 執行驗證並自動修正問題

#### 3. 顯示工作表資訊
```javascript
displaySheetsInfo()
```
**功能**: 顯示所有工作表的詳細資訊

#### 4. 重置工作表結構 (謹慎使用)
```javascript
resetSheetsStructure()
```
**⚠️ 警告**: 此功能會清除所有資料並重建結構

## 📊 執行結果範例

### 成功執行的日誌輸出
```
開始驗證工作表結構...
驗證知識庫工作表...
✅ Sheet1 標題行正確
驗證對話記錄相關工作表...
建立工作表: 對話記錄
✅ 對話記錄 標題行建立完成
建立工作表: Users
✅ Users 標題行建立完成
建立工作表: Errors
✅ Errors 標題行建立完成
✅ 所有工作表結構驗證通過
```

### 工作表資訊總覽
```
=== 工作表資訊總覽 ===

📊 知識庫試算表: 雲林光合菌協會資料庫
🔗 連結: https://docs.google.com/spreadsheets/d/1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk/edit
   📋 Sheet1: 4行 x 3欄
   📋 對話記錄: 1行 x 5欄
   📋 Users: 1行 x 4欄
   📋 Errors: 1行 x 5欄
```

## 🔧 技術細節

### 驗證流程

1. **配置檢查**: 確認Google Sheets ID已正確設定
2. **工作表掃描**: 檢查每個必要工作表是否存在
3. **結構驗證**: 驗證標題行是否正確
4. **自動修正**: 建立缺失的工作表和欄位
5. **格式設定**: 應用標準格式和樣式
6. **範例資料**: 為知識庫新增基本範例資料

### 安全機制

- **非破壞性操作**: 只新增缺失的內容，不會刪除現有資料
- **備份建議**: 重要操作前建議先備份資料
- **錯誤處理**: 完善的錯誤捕獲和日誌記錄
- **確認機制**: 重置功能需要手動確認

## 🛠️ 故障排除

### 常見問題

#### 1. 權限錯誤
**症狀**: 無法存取Google Sheets
**解決方案**: 
- 確認Google Sheets的分享權限
- 檢查Google Apps Script的授權

#### 2. 工作表ID錯誤
**症狀**: 找不到指定的試算表
**解決方案**:
- 確認`KNOWLEDGE_BASE_SHEET_ID`設定正確
- 確認`CONVERSATION_LOG_SHEET_ID`設定正確

#### 3. 建立工作表失敗
**症狀**: 無法建立新的工作表
**解決方案**:
- 檢查試算表是否已達到工作表數量限制
- 確認有編輯權限

### 錯誤代碼說明

| 錯誤訊息 | 原因 | 解決方案 |
|---------|------|---------|
| `Sheet ID 未設定` | 配置屬性缺失 | 設定正確的Sheet ID |
| `無法存取試算表` | 權限問題 | 檢查分享權限 |
| `建立工作表失敗` | 權限或限制問題 | 檢查編輯權限 |

## 📈 效益與優勢

### 🎯 自動化優勢
- **零手動設定**: 系統自動建立和維護工作表結構
- **錯誤預防**: 避免因工作表結構錯誤導致的系統故障
- **一致性保證**: 確保所有環境的工作表結構一致

### 🔒 可靠性提升
- **自我修復**: 系統能自動修復常見的結構問題
- **監控能力**: 持續監控工作表狀態
- **日誌記錄**: 詳細記錄所有操作和錯誤

### 🚀 維護效率
- **減少人工干預**: 大幅減少手動維護工作
- **快速部署**: 新環境部署時自動建立所需結構
- **標準化管理**: 統一的工作表結構和格式

## 📞 技術支援

如果您在使用自動驗證功能時遇到問題：

1. **檢查執行日誌**: 在GAS編輯器中查看詳細的執行日誌
2. **手動執行測試**: 運行`manualValidateSheets()`函數
3. **聯繫技術支援**: <EMAIL>

## 🔄 版本更新

### v1.1.0 新增功能
- ✅ 自動工作表結構驗證
- ✅ 智能工作表建立和修正
- ✅ 範例資料自動填入
- ✅ 工作表資訊總覽顯示
- ✅ 安全的重置功能

### 未來規劃
- 🔜 工作表資料備份功能
- 🔜 更多自訂驗證規則
- 🔜 批量資料匯入功能
- 🔜 工作表效能優化

---

**這個新功能讓您的Line BOT系統更加智能和可靠！** 🎉

系統現在可以自動維護Google Sheets的結構，確保所有功能正常運作，大幅減少手動維護的工作量。
