# 雲林光合菌協會 Line BOT 快速檢查腳本
# Quick Check Script for Line BOT Project

Write-Host "=== 雲林光合菌協會 Line BOT 快速檢查 ===" -ForegroundColor Green

# 專案資訊
$GAS_PROJECT_ID = "1mWN5lA7Nu0m9Do04N12EQ1ECCj75RWQp6nViTHosM27Es8Kt310bcRFI"
$SPREADSHEET_ID = "1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk"

Write-Host "`n專案資訊：" -ForegroundColor Cyan
Write-Host "GAS專案ID: $GAS_PROJECT_ID"
Write-Host "Sheets ID: $SPREADSHEET_ID"

# 檢查必要檔案
Write-Host "`n檢查專案檔案：" -ForegroundColor Cyan

$files = @("code.gs", "appsscript.json", "README.md", "knowledge_base.csv")
$allExists = $true

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file" -ForegroundColor Red
        $allExists = $false
    }
}

# 檢查docs目錄
if (Test-Path "docs") {
    Write-Host "✅ docs目錄" -ForegroundColor Green
} else {
    Write-Host "❌ docs目錄" -ForegroundColor Red
    $allExists = $false
}

# 檢查code.gs內容
if (Test-Path "code.gs") {
    $content = Get-Content "code.gs" -Raw
    $lineCount = (Get-Content "code.gs").Count
    Write-Host "`ncode.gs 檔案資訊：" -ForegroundColor Cyan
    Write-Host "總行數: $lineCount"
    
    if ($content -match "v1\.0\.0") {
        Write-Host "✅ 版本: v1.0.0" -ForegroundColor Green
    }
    
    if ($content -match "雲林光合菌協會") {
        Write-Host "✅ 品牌: 雲林光合菌協會" -ForegroundColor Green
    }
}

# 生成快速存取資訊
Write-Host "`n快速存取連結：" -ForegroundColor Cyan
Write-Host "Google Apps Script:"
Write-Host "https://script.google.com/d/$GAS_PROJECT_ID/edit" -ForegroundColor Blue
Write-Host "`nGoogle Sheets:"
Write-Host "https://docs.google.com/spreadsheets/d/$SPREADSHEET_ID/edit" -ForegroundColor Blue

# 配置提醒
Write-Host "`n配置提醒：" -ForegroundColor Yellow
Write-Host "請在Google Apps Script中設定以下屬性："
Write-Host "KNOWLEDGE_BASE_SHEET_ID = $SPREADSHEET_ID"
Write-Host "CONVERSATION_LOG_SHEET_ID = $SPREADSHEET_ID"
Write-Host "CHANNEL_ACCESS_TOKEN = [從Line Console取得]"
Write-Host "CHANNEL_SECRET = [從Line Console取得]"

# 下一步
Write-Host "`n下一步：" -ForegroundColor Green
if ($allExists) {
    Write-Host "1. 開啟 CONFIGURATION_SETUP.md 進行詳細配置"
    Write-Host "2. 設定Line Developer Console"
    Write-Host "3. 部署並測試系統"
} else {
    Write-Host "請先確保所有必要檔案都存在"
}

Write-Host "`n檢查完成！" -ForegroundColor Green
