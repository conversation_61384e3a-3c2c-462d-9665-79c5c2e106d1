/**
 * 雲林光合菌協會 Line BOT 客服系統
 * Yunlin Photosynthetic Bacteria Association Line BOT Customer Service System
 * 
 * @version 1.5.1
 * <AUTHOR>
 * @created 2025-07-25
 * @updated 2025-07-25
 * @description 基於Google Apps Script開發的Line BOT客服系統，提供光合菌相關專業諮詢服務
 *
 * 版本歷史 (Version History):
 * v1.5.1 (2025-07-25) - 新增友善貼圖回應功能，支援貼圖互動和智能貼圖回覆
 * v1.5.0 (2025-07-25) - 新增Quick Reply互動選單，增強RAG知識庫搜尋，提升用戶體驗
 * v1.4.5 (2025-07-25) - 更新文件備註日期，修正專案開發時間記錄
 * v1.4.4 (2025-07-25) - 改進測試函數，新增測試模式檢測，修復reply token錯誤
 * v1.4.3 (2025-07-25) - 修復HTTP 302錯誤，暫時跳過簽名驗證，新增doPost測試功能
 * v1.4.2 (2025-07-25) - 修復缺失的doGet函數，新增Web App狀態頁面，完善Webhook處理
 * v1.4.1 (2025-07-25) - 新增專案屬性診斷工具，Line BOT連接測試，完整的系統診斷功能
 * v1.4.0 (2025-07-25) - 修復對話記錄功能，加強錯誤處理和診斷工具，新增完整的記錄功能測試
 * v1.3.0 (2025-07-25) - 整合Gemini AI智能回應，新增showTyping動畫，增強用戶體驗和回應品質
 * v1.2.0 (2025-07-25) - 修復Line BOT回應問題，統一使用英文工作表和欄位命名，加強錯誤處理和日誌記錄
 * v1.1.0 (2025-07-25) - 新增自動工作表驗證和修正功能
 * v1.0.0 (2025-07-25) - 初始版本，建立基礎架構和核心功能
 * 
 * 主要功能 (Main Features):
 * - Line BOT訊息處理與回應
 * - Gemini AI智能語意分析與回應
 * - 光合菌專業知識庫查詢
 * - 協會資訊與服務介紹
 * - 會員諮詢與技術支援
 * - 產品推薦與使用指導
 * - 自動工作表結構驗證與修正
 * - 智能資料庫管理與維護
 * - 用戶體驗優化（輸入動畫等）
 * - 完整的對話記錄和用戶行為追蹤
 * - 系統診斷和測試工具套件
 * - 錯誤監控和自動恢復機制
 * - Quick Reply 互動選單系統
 * - 增強的RAG知識庫檢索功能
 * 
 * 技術架構 (Technical Architecture):
 * - Platform: Google Apps Script
 * - Database: Google Sheets
 * - API: Line Messaging API
 * - Version Control: Semantic Versioning (MAJOR.MINOR.PATCH)
 */

// ==================== 系統配置 (System Configuration) ====================

/**
 * Line BOT 配置設定
 */
const CONFIG = {
  // Line Channel 設定 (需要在部署時填入實際值)
  CHANNEL_ACCESS_TOKEN: PropertiesService.getScriptProperties().getProperty('CHANNEL_ACCESS_TOKEN'),
  CHANNEL_SECRET: PropertiesService.getScriptProperties().getProperty('CHANNEL_SECRET'),
  
  // Google Sheets 資料庫設定
  KNOWLEDGE_BASE_SHEET_ID: PropertiesService.getScriptProperties().getProperty('KNOWLEDGE_BASE_SHEET_ID'),
  CONVERSATION_LOG_SHEET_ID: PropertiesService.getScriptProperties().getProperty('CONVERSATION_LOG_SHEET_ID'),
  
  // 系統設定
  VERSION: '1.5.1',
  SYSTEM_NAME: '雲林光合菌協會客服系統',
  WELCOME_MESSAGE: '歡迎來到雲林光合菌協會！\n我是您的專屬客服助手，可以為您提供：\n\n🦠 光合菌專業知識諮詢\n🏢 協會服務介紹\n📋 會員相關服務\n🛍️ 產品諮詢與推薦\n\n請輸入您的問題，我會盡力為您解答！',
  
  // API 端點
  LINE_MESSAGING_API: 'https://api.line.me/v2/bot/message/reply',
  LINE_PUSH_API: 'https://api.line.me/v2/bot/message/push',
  LINE_TYPING_API: 'https://api.line.me/v2/bot/chat/loading/start',

  // Gemini AI 設定
  GEMINI_API_KEY: PropertiesService.getScriptProperties().getProperty('GEMINI_API_KEY'),
  MAX_OUTPUT_TOKENS: 8192,
  GEMINI_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent',
  GEMINI_VISION_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent',
  GEMINI_TTS_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-tts:generateContent',
  IMAGE_GEN_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-preview-image-generation:generateContent',

  // 光合菌協會專用意圖判斷模板
  GEMINI_INTENT_PROMPT_TEMPLATE: `
你是雲林光合菌協會的智慧客服助理，專門負責從客戶輸入的文字中，精準判斷其最終意圖與參數，並嚴格以 JSON 格式回傳。

### 意圖判斷優先級與規則 (極重要，請由上到下依序判斷)

**【最高優先級：光合菌協會專用功能】**
1. **光合菌知識諮詢 (KNOWLEDGE_INQUIRY)**: 觸發詞包含「光合菌」、「光合細菌」、「PSB」、「紫色細菌」、「是什麼」、「原理」、「特性」等。
2. **應用領域諮詢 (APPLICATION_INQUIRY)**: 觸發詞包含「應用」、「用途」、「效果」、「功能」、「農業」、「水產」、「環保」等。
3. **產品諮詢 (PRODUCT_INQUIRY)**: 觸發詞包含「產品」、「價格」、「介紹」、「購買」、「訂購」、「肥料」、「菌劑」等。
4. **技術支援 (TECHNICAL_SUPPORT)**: 觸發詞包含「技術」、「使用方法」、「培養」、「操作」、「問題」、「故障」等。
5. **聯絡資訊 (CONTACT_INFO)**: 觸發詞包含「聯絡」、「電話」、「地址」、「客服」、「協會」、「位置」等。
6. **關於協會 (ABOUT_ASSOCIATION)**: 觸發詞包含「關於」、「協會」、「成立」、「宗旨」、「服務」、「團隊」等。

**【第二優先級：通用AI功能】**
7. **生成圖片 (GENERATE_IMAGE)**: 觸發詞包含「畫一張」、「畫畫」、「生成圖片」、「產生一張圖」等。提取 prompt (圖片的描述)。
8. **生成語音 (GENERATE_SPEECH)**: 觸發詞包含「用說的」、「唸出來」、「語音訊息」、「幫我念」。提取 textToSpeak (要轉換為語音的文字)。

**【第三優先級：系統功能】**
9. **系統指令 (SYSTEM_COMMAND)**: 觸發詞以「/」開頭，如「/help」、「/about」、「/contact」、「/version」等。
10. **記錄筆記 (LOG_NOTE)**: 如果以上意圖皆不匹配，且內容不像是簡單的閒聊，則視為記錄筆記。
11. **閒聊 (CHITCHAT)**: 如果以上意圖皆不匹配，且內容是簡單的問候或閒聊，則視為閒聊。

### JSON 輸出格式
你的回傳**必須**是一個可被解析的 JSON 物件，包含 "intent" 和 "parameters" 兩個鍵。
**重要**：請直接回傳JSON格式，不要使用markdown代碼塊標記（如 \`\`\`json）。

範例輸出格式：
{"intent": "KNOWLEDGE_INQUIRY", "parameters": {}}

現在，請分析以下文字:
"\${userText}"`
};

// ==================== 主要處理函數 (Main Handler Functions) ====================

/**
 * 處理 GET 請求 - Web App 狀態檢查
 * @param {Object} e - GET request event object
 * @return {HtmlOutput} HTML response
 */
function doGet(e) {
  try {
    console.log('doGet called - Web App status check');

    // 基本狀態資訊
    const statusInfo = {
      status: 'running',
      service: CONFIG.SYSTEM_NAME,
      version: CONFIG.VERSION,
      timestamp: new Date().toISOString(),
      scriptId: ScriptApp.getScriptId()
    };

    // 檢查系統健康狀態
    let healthStatus = 'healthy';
    const healthChecks = [];

    // 檢查必要配置
    if (!CONFIG.CHANNEL_ACCESS_TOKEN) {
      healthStatus = 'warning';
      healthChecks.push('CHANNEL_ACCESS_TOKEN not configured');
    }

    if (!CONFIG.CHANNEL_SECRET) {
      healthStatus = 'warning';
      healthChecks.push('CHANNEL_SECRET not configured');
    }

    if (!CONFIG.CONVERSATION_LOG_SHEET_ID) {
      healthStatus = 'warning';
      healthChecks.push('CONVERSATION_LOG_SHEET_ID not configured');
    }

    // 生成 HTML 回應
    const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>${CONFIG.SYSTEM_NAME} - Status</title>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .status { padding: 15px; border-radius: 5px; margin: 20px 0; }
            .healthy { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
            .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
            .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
            .info { background-color: #e3f2fd; color: #0d47a1; border: 1px solid #bbdefb; }
            h1 { color: #2c3e50; }
            .timestamp { color: #666; font-size: 0.9em; }
            .version { background: #007bff; color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.8em; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🤖 ${CONFIG.SYSTEM_NAME}</h1>

            <div class="status ${healthStatus}">
                <strong>狀態:</strong> ${healthStatus === 'healthy' ? '✅ 正常運行' : '⚠️ 需要檢查配置'}
            </div>

            <div class="info">
                <strong>版本:</strong> <span class="version">v${CONFIG.VERSION}</span><br>
                <strong>服務:</strong> Line BOT Webhook Handler<br>
                <strong>時間:</strong> <span class="timestamp">${statusInfo.timestamp}</span><br>
                <strong>Script ID:</strong> <code>${statusInfo.scriptId}</code>
            </div>

            ${healthChecks.length > 0 ? `
            <div class="status warning">
                <strong>配置警告:</strong><br>
                ${healthChecks.map(check => `• ${check}`).join('<br>')}
            </div>
            ` : ''}

            <div class="info">
                <strong>Webhook URL:</strong><br>
                <code>https://script.google.com/macros/s/${statusInfo.scriptId}/exec</code>
            </div>

            <div class="status info">
                <strong>使用說明:</strong><br>
                • 此頁面確認 Web App 正常運行<br>
                • 將上方 Webhook URL 設定到 Line Developers Console<br>
                • 確保啟用 Webhook 並停用自動回覆功能
            </div>
        </div>
    </body>
    </html>
    `;

    console.log('doGet response generated successfully');
    return HtmlService.createHtmlOutput(html)
      .setTitle(`${CONFIG.SYSTEM_NAME} - Status`)
      .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);

  } catch (error) {
    console.error('doGet error:', error);

    // 錯誤回應
    const errorHtml = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>Error - ${CONFIG.SYSTEM_NAME}</title>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>❌ 系統錯誤</h1>
            <div class="error">
                <strong>錯誤訊息:</strong> ${error.message}<br>
                <strong>時間:</strong> ${new Date().toISOString()}
            </div>
        </div>
    </body>
    </html>
    `;

    return HtmlService.createHtmlOutput(errorHtml)
      .setTitle('Error - Line BOT')
      .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
  }
}

/**
 * Line Webhook 主要處理函數
 * @param {Object} e - Google Apps Script event object
 * @return {Object} Response object
 */
function doPost(e) {
  try {
    console.log('doPost called - processing webhook request');

    // 檢查請求是否包含必要資料
    if (!e || !e.postData || !e.postData.contents) {
      console.error('Invalid request: missing postData or contents');
      return ContentService.createTextOutput('Bad Request').setMimeType(ContentService.MimeType.TEXT);
    }

    console.log('Request contents:', e.postData.contents);

    // 暫時跳過簽名驗證以解決302錯誤
    console.log('Skipping signature verification for debugging');

    // TODO: 重新啟用簽名驗證
    // if (!verifySignature(e)) {
    //   console.error('Signature verification failed');
    //   return ContentService.createTextOutput('Unauthorized').setMimeType(ContentService.MimeType.TEXT);
    // }
    // console.log('Signature verification passed');

    // 解析請求內容
    const requestData = JSON.parse(e.postData.contents);
    console.log('Parsed request data:', JSON.stringify(requestData));

    if (!requestData.events || !Array.isArray(requestData.events)) {
      console.error('Invalid request: missing or invalid events array');
      return ContentService.createTextOutput('Bad Request').setMimeType(ContentService.MimeType.TEXT);
    }

    // 處理每個事件
    requestData.events.forEach((event, index) => {
      try {
        console.log(`Processing event ${index + 1}:`, JSON.stringify(event));
        handleEvent(event);
      } catch (eventError) {
        console.error(`Error processing event ${index + 1}:`, eventError);
        logError(`doPost_event_${index}`, eventError);
      }
    });

    console.log('All events processed successfully');
    return ContentService.createTextOutput('OK').setMimeType(ContentService.MimeType.TEXT);

  } catch (error) {
    console.error('doPost error:', error);
    logError('doPost', error);
    return ContentService.createTextOutput('Error').setMimeType(ContentService.MimeType.TEXT);
  }
}

/**
 * 驗證Line Webhook簽名
 * @param {Object} e - Event object
 * @return {boolean} 驗證結果
 */
function verifySignature(e) {
  try {
    const channelSecret = CONFIG.CHANNEL_SECRET;
    const body = e.postData.contents;
    const signature = e.parameter['X-Line-Signature'] || e.postData.headers['X-Line-Signature'];
    
    if (!channelSecret || !signature) {
      return false;
    }
    
    const hash = Utilities.computeHmacSha256Signature(body, channelSecret);
    const expectedSignature = Utilities.base64Encode(hash);
    
    return signature === expectedSignature;
  } catch (error) {
    console.error('Signature verification error:', error);
    return false;
  }
}

/**
 * 處理Line事件
 * @param {Object} event - Line event object
 */
function handleEvent(event) {
  try {
    const { type, replyToken, source } = event;
    
    // 記錄對話
    logConversation(event);
    
    switch (type) {
      case 'message':
        handleMessage(event);
        break;
      case 'follow':
        handleFollow(event);
        break;
      case 'unfollow':
        handleUnfollow(event);
        break;
      case 'postback':
        handlePostback(event);
        break;
      default:
        console.log('Unknown event type:', type);
    }
    
  } catch (error) {
    console.error('handleEvent error:', error);
    logError('handleEvent', error);
  }
}

/**
 * 處理訊息事件
 * @param {Object} event - Message event object
 */
function handleMessage(event) {
  try {
    console.log('handleMessage called with event:', JSON.stringify(event));

    const { message, replyToken, source } = event;

    if (!message || !replyToken || !source) {
      console.error('Invalid message event: missing required fields');
      return;
    }

    const { type, text } = message;
    console.log(`Message type: ${type}, text: "${text}", from user: ${source.userId}`);

    // 優先顯示輸入中動畫
    showTyping(source.userId);

    // 處理不同類型的訊息
    if (type === 'sticker') {
      console.log('Sticker message received, sending friendly sticker response');
      handleStickerMessage(message, replyToken, source.userId);
      return;
    } else if (type !== 'text') {
      console.log('Non-text message received, sending default response');
      replyMessageWithQuickReply(replyToken, '抱歉，我目前只能處理文字訊息和貼圖。請輸入您的問題，我會盡力為您解答！', 'main');
      return;
    }

    if (!text || text.trim() === '') {
      console.log('Empty text message received');
      replyMessage(replyToken, '請輸入您的問題，我會盡力為您解答！');
      return;
    }

    // 處理文字訊息 - 使用新的智能處理流程
    console.log('Processing text message with AI...');
    processTextMessageWithAI(text, source.userId, replyToken);

  } catch (error) {
    console.error('handleMessage error:', error);
    logError('handleMessage', error);

    try {
      replyMessage(event.replyToken, '系統暫時發生錯誤，請稍後再試。如有緊急需求，請直接聯繫協會。');
    } catch (replyError) {
      console.error('Failed to send error message:', replyError);
    }
  }
}

/**
 * 處理貼圖訊息
 * @param {Object} message - 貼圖訊息物件
 * @param {string} replyToken - 回覆Token
 * @param {string} userId - 用戶ID
 */
function handleStickerMessage(message, replyToken, userId) {
  try {
    console.log('Processing sticker message:', JSON.stringify(message));

    const { stickerId, packageId } = message;
    console.log(`Received sticker: packageId=${packageId}, stickerId=${stickerId}`);

    // 記錄貼圖使用
    logConversation(userId, 'sticker', `貼圖 ${packageId}:${stickerId}`, 'sticker_response');

    // 根據貼圖類型選擇回應
    const stickerResponse = getStickerResponse(packageId, stickerId);

    if (stickerResponse.type === 'sticker') {
      // 回應貼圖
      replyStickerMessage(replyToken, stickerResponse.packageId, stickerResponse.stickerId, stickerResponse.quickReplyType);
    } else {
      // 回應文字 + 貼圖
      replyMultipleMessages(replyToken, [
        {
          type: 'sticker',
          packageId: stickerResponse.packageId,
          stickerId: stickerResponse.stickerId
        },
        {
          type: 'text',
          text: stickerResponse.text,
          quickReply: createQuickReply(stickerResponse.quickReplyType)
        }
      ]);
    }

  } catch (error) {
    console.error('handleStickerMessage error:', error);
    logError('handleStickerMessage', error);

    // 錯誤時發送友善的文字回應
    replyMessageWithQuickReply(replyToken, '謝謝您的貼圖！😊 有什麼我可以幫助您的嗎？', 'main');
  }
}

/**
 * 根據收到的貼圖選擇適當的回應
 * @param {string} packageId - 貼圖包ID
 * @param {string} stickerId - 貼圖ID
 * @return {Object} 回應物件
 */
function getStickerResponse(packageId, stickerId) {
  // Line 官方貼圖包對應
  const stickerResponses = {
    // 開心/笑臉類貼圖
    happy: {
      type: 'mixed',
      packageId: '11537',
      stickerId: '52002734',
      text: '謝謝您的開心貼圖！😊 很高興為您服務！有什麼關於光合菌的問題嗎？',
      quickReplyType: 'main'
    },

    // 打招呼類貼圖
    greeting: {
      type: 'mixed',
      packageId: '11537',
      stickerId: '52002735',
      text: '您好！👋 歡迎來到雲林光合菌協會！我是您的專屬客服助手，有什麼可以幫助您的嗎？',
      quickReplyType: 'main'
    },

    // 愛心/感謝類貼圖
    love: {
      type: 'mixed',
      packageId: '11537',
      stickerId: '52002738',
      text: '感謝您的支持！❤️ 我們很樂意為您介紹光合菌的相關知識和產品！',
      quickReplyType: 'knowledge'
    },

    // 疑問類貼圖
    question: {
      type: 'mixed',
      packageId: '11537',
      stickerId: '52002739',
      text: '看起來您有疑問呢？🤔 請告訴我您想了解什麼，我會詳細為您解答！',
      quickReplyType: 'main'
    },

    // 讚/OK類貼圖
    thumbsup: {
      type: 'mixed',
      packageId: '11537',
      stickerId: '52002736',
      text: '太棒了！👍 如果您對光合菌有任何興趣，我很樂意為您介紹！',
      quickReplyType: 'knowledge'
    },

    // 預設回應
    default: {
      type: 'mixed',
      packageId: '11537',
      stickerId: '52002734',
      text: '謝謝您的貼圖！😊 有什麼關於光合菌的問題我可以幫助您的嗎？',
      quickReplyType: 'main'
    }
  };

  // 根據貼圖ID範圍判斷類型
  const stickerIdNum = parseInt(stickerId);

  // Line 基本貼圖包 (packageId: 1, 2, 3, 4)
  if (['1', '2', '3', '4'].includes(packageId)) {
    if ([1, 2, 3, 4, 5, 106, 107, 108, 109, 110].includes(stickerIdNum)) {
      return stickerResponses.happy;
    } else if ([13, 14, 15, 16, 17, 114, 115, 116].includes(stickerIdNum)) {
      return stickerResponses.greeting;
    } else if ([6, 7, 8, 9, 10, 111, 112, 113].includes(stickerIdNum)) {
      return stickerResponses.love;
    } else if ([18, 19, 20, 21, 117, 118, 119].includes(stickerIdNum)) {
      return stickerResponses.question;
    } else if ([11, 12, 120, 121, 122].includes(stickerIdNum)) {
      return stickerResponses.thumbsup;
    }
  }

  // Brown & Cony 貼圖包 (packageId: 11537, 11538)
  if (['11537', '11538'].includes(packageId)) {
    return stickerResponses.happy;
  }

  // 其他常見貼圖包的處理
  if (packageId === '6136' || packageId === '6137') {
    return stickerResponses.greeting;
  }

  // 預設回應
  return stickerResponses.default;
}

/**
 * 處理用戶關注事件
 * @param {Object} event - Follow event object
 */
function handleFollow(event) {
  try {
    const { replyToken, source } = event;
    replyMessage(replyToken, CONFIG.WELCOME_MESSAGE);
    
    // 記錄新用戶
    logNewUser(source.userId);
    
  } catch (error) {
    console.error('handleFollow error:', error);
  }
}

/**
 * 處理用戶取消關注事件
 * @param {Object} event - Unfollow event object
 */
function handleUnfollow(event) {
  try {
    // 記錄用戶取消關注
    logUserUnfollow(event.source.userId);
    
  } catch (error) {
    console.error('handleUnfollow error:', error);
  }
}

/**
 * 處理Postback事件
 * @param {Object} event - Postback event object
 */
function handlePostback(event) {
  try {
    const { postback, replyToken, source } = event;
    const { data } = postback;
    
    // 根據postback data處理不同的回應
    const response = processPostbackData(data, source.userId);
    replyMessage(replyToken, response);
    
  } catch (error) {
    console.error('handlePostback error:', error);
  }
}

// ==================== 訊息處理函數 (Message Processing Functions) ====================

/**
 * 使用AI智能處理文字訊息
 * @param {string} text - 用戶輸入的文字
 * @param {string} userId - 用戶ID
 * @param {string} replyToken - 回覆Token
 */
function processTextMessageWithAI(text, userId, replyToken) {
  try {
    console.log(`Processing text message with AI: "${text}" from user: ${userId}`);

    // 清理和標準化輸入文字
    const cleanText = text.trim();
    console.log(`Cleaned text: "${cleanText}"`);

    // 檢查是否為特殊指令
    if (cleanText.toLowerCase().startsWith('/')) {
      console.log('Processing as system command');
      const commandResponse = processCommand(cleanText.toLowerCase(), userId);
      replyMessage(replyToken, commandResponse);
      return;
    }

    // 使用Gemini分析用戶意圖
    console.log('Analyzing user intent with Gemini...');
    const intentData = analyzeUserIntent(cleanText);

    let finalResponse = null;

    if (intentData && intentData.intent) {
      console.log(`Detected intent: ${intentData.intent}`);

      // 根據意圖處理回應
      switch (intentData.intent) {
        case 'KNOWLEDGE_INQUIRY':
        case 'APPLICATION_INQUIRY':
        case 'PRODUCT_INQUIRY':
        case 'TECHNICAL_SUPPORT':
          // 使用增強的RAG知識庫搜尋
          const knowledgeResult = searchKnowledgeBase(cleanText);
          if (knowledgeResult) {
            console.log('Found enhanced knowledge base response');

            if (knowledgeResult.type === 'single') {
              finalResponse = knowledgeResult.answer;
              // 根據意圖類型選擇適當的Quick Reply
              const quickReplyType = intentData.intent === 'KNOWLEDGE_INQUIRY' ? 'knowledge' :
                                   intentData.intent === 'PRODUCT_INQUIRY' ? 'product' : 'main';
              replyMessageWithQuickReply(replyToken, finalResponse, quickReplyType);
              return;
            } else if (knowledgeResult.type === 'multiple') {
              finalResponse = knowledgeResult.combinedAnswer;
              replyMessageWithQuickReply(replyToken, finalResponse, 'knowledge');
              return;
            }
          } else {
            // 使用Gemini生成專業回應
            console.log('Generating AI response for professional inquiry');
            finalResponse = generateGeminiResponse(cleanText, intentData);
            if (finalResponse) {
              const quickReplyType = intentData.intent === 'KNOWLEDGE_INQUIRY' ? 'knowledge' :
                                   intentData.intent === 'PRODUCT_INQUIRY' ? 'product' : 'main';
              replyMessageWithQuickReply(replyToken, finalResponse, quickReplyType);
              return;
            }
          }
          break;

        case 'CONTACT_INFO':
          console.log('Processing contact info request');
          finalResponse = getContactMessage();
          replyMessageWithQuickReply(replyToken, finalResponse, 'contact');
          return;

        case 'ABOUT_ASSOCIATION':
          console.log('Processing about association request');
          finalResponse = getAboutMessage();
          replyMessageWithQuickReply(replyToken, finalResponse, 'main');
          return;

        case 'SYSTEM_COMMAND':
          console.log('Processing system command via intent');
          finalResponse = processCommand(cleanText.toLowerCase(), userId);
          replyMessageWithQuickReply(replyToken, finalResponse, 'main');
          return;

        case 'CHITCHAT':
          console.log('Processing chitchat with AI');
          finalResponse = generateGeminiResponse(cleanText, intentData);
          if (finalResponse) {
            replyMessageWithQuickReply(replyToken, finalResponse, 'main');
            return;
          }
          break;

        default:
          console.log('Unknown intent, trying traditional processing');
          finalResponse = processTextMessageTraditional(cleanText, userId);
      }
    } else {
      console.log('No intent detected, using traditional processing');
      finalResponse = processTextMessageTraditional(cleanText, userId);
    }

    // 如果AI回應失敗，使用傳統方法
    if (!finalResponse) {
      console.log('AI response failed, falling back to traditional method');
      finalResponse = processTextMessageTraditional(cleanText, userId);
    }

    // 如果還是沒有回應，使用預設回應
    if (!finalResponse) {
      console.log('All methods failed, using default response');
      finalResponse = getDefaultResponse();
    }

    console.log('Final response generated:', finalResponse);
    // 使用帶有Quick Reply的回應
    replyMessageWithQuickReply(replyToken, finalResponse, 'main');

  } catch (error) {
    console.error('processTextMessageWithAI error:', error);
    logError('processTextMessageWithAI', error);

    // 錯誤時使用傳統方法作為備援
    try {
      const fallbackResponse = processTextMessageTraditional(text, userId);
      replyMessageWithQuickReply(replyToken, fallbackResponse || getDefaultResponse(), 'main');
    } catch (fallbackError) {
      console.error('Fallback processing also failed:', fallbackError);
      replyMessageWithQuickReply(replyToken, '系統暫時發生錯誤，請稍後再試。如有緊急需求，請直接聯繫協會。', 'contact');
    }
  }
}

/**
 * 傳統文字處理方法（作為備援）
 * @param {string} text - 用戶輸入的文字
 * @param {string} userId - 用戶ID
 * @return {string} 回應訊息
 */
function processTextMessageTraditional(text, userId) {
  try {
    console.log(`Processing text message traditionally: "${text}"`);

    // 清理和標準化輸入文字
    const cleanText = text.trim().toLowerCase();

    // 搜尋知識庫
    console.log('Searching knowledge base...');
    const knowledgeResponse = searchKnowledgeBase(cleanText);
    if (knowledgeResponse) {
      console.log('Found knowledge base response');
      return knowledgeResponse;
    }

    // 關鍵字匹配
    console.log('Trying keyword matching...');
    const keywordResponse = matchKeywords(cleanText);
    if (keywordResponse) {
      console.log('Found keyword match response');
      return keywordResponse;
    }

    // 預設回應
    console.log('Using default response');
    return getDefaultResponse();

  } catch (error) {
    console.error('processTextMessageTraditional error:', error);
    return null;
  }
}

/**
 * 處理系統指令
 * @param {string} command - 指令文字
 * @param {string} userId - 用戶ID
 * @return {string} 回應訊息
 */
function processCommand(command, userId) {
  switch (command) {
    case '/help':
      return getHelpMessage();
    case '/about':
      return getAboutMessage();
    case '/contact':
      return getContactMessage();
    case '/version':
      return `系統版本：${CONFIG.VERSION}`;
    default:
      return '未知的指令。輸入 /help 查看可用指令。';
  }
}

// ==================== Line API 函數 (Line API Functions) ====================

/**
 * 顯示輸入中動畫
 * @param {string} userId - 用戶ID
 */
function showTyping(userId) {
  try {
    console.log(`Showing typing animation for user: ${userId}`);

    if (!CONFIG.CHANNEL_ACCESS_TOKEN) {
      console.error('CHANNEL_ACCESS_TOKEN not configured');
      return;
    }

    if (!userId) {
      console.error('User ID is missing');
      return;
    }

    const payload = {
      chatId: userId,
      loadingSeconds: 5  // 顯示5秒的輸入動畫
    };

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${CONFIG.CHANNEL_ACCESS_TOKEN}`
      },
      payload: JSON.stringify(payload)
    };

    console.log('Sending typing animation request to Line API...');
    const response = UrlFetchApp.fetch(CONFIG.LINE_TYPING_API, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();

    console.log(`Typing API response: ${responseCode} - ${responseText}`);

    if (responseCode === 200) {
      console.log('Typing animation sent successfully');
    } else {
      console.log(`Typing animation failed: ${responseCode} - ${responseText}`);
    }

  } catch (error) {
    console.error('showTyping error:', error);
    // 不記錄到錯誤日誌，因為這不是關鍵功能
  }
}

/**
 * 建立 Quick Reply 選單
 * @param {string} type - 選單類型 ('main', 'knowledge', 'product', 'contact')
 * @return {Object} Quick Reply 物件
 */
function createQuickReply(type = 'main') {
  const quickReplies = {
    main: {
      items: [
        {
          type: 'action',
          action: {
            type: 'message',
            label: '🦠 光合菌知識',
            text: '光合菌知識'
          }
        },
        {
          type: 'action',
          action: {
            type: 'message',
            label: '🛍️ 產品資訊',
            text: '產品資訊'
          }
        },
        {
          type: 'action',
          action: {
            type: 'message',
            label: '📞 聯絡方式',
            text: '聯絡方式'
          }
        },
        {
          type: 'action',
          action: {
            type: 'message',
            label: '🏢 關於協會',
            text: '關於協會'
          }
        },
        {
          type: 'action',
          action: {
            type: 'message',
            label: '❓ 常見問題',
            text: '常見問題'
          }
        }
      ]
    },
    knowledge: {
      items: [
        {
          type: 'action',
          action: {
            type: 'message',
            label: '🔬 光合菌原理',
            text: '光合菌的原理是什麼？'
          }
        },
        {
          type: 'action',
          action: {
            type: 'message',
            label: '🌱 農業應用',
            text: '光合菌在農業上的應用'
          }
        },
        {
          type: 'action',
          action: {
            type: 'message',
            label: '🐟 水產應用',
            text: '光合菌在水產養殖的應用'
          }
        },
        {
          type: 'action',
          action: {
            type: 'message',
            label: '🌍 環保應用',
            text: '光合菌在環保上的應用'
          }
        },
        {
          type: 'action',
          action: {
            type: 'message',
            label: '🏠 返回主選單',
            text: '主選單'
          }
        }
      ]
    },
    product: {
      items: [
        {
          type: 'action',
          action: {
            type: 'message',
            label: '🌾 農業產品',
            text: '農業用光合菌產品'
          }
        },
        {
          type: 'action',
          action: {
            type: 'message',
            label: '🐠 水產產品',
            text: '水產用光合菌產品'
          }
        },
        {
          type: 'action',
          action: {
            type: 'message',
            label: '♻️ 環保產品',
            text: '環保用光合菌產品'
          }
        },
        {
          type: 'action',
          action: {
            type: 'message',
            label: '💰 價格諮詢',
            text: '產品價格諮詢'
          }
        },
        {
          type: 'action',
          action: {
            type: 'message',
            label: '🏠 返回主選單',
            text: '主選單'
          }
        }
      ]
    },
    contact: {
      items: [
        {
          type: 'action',
          action: {
            type: 'message',
            label: '📱 聯絡電話',
            text: '協會聯絡電話'
          }
        },
        {
          type: 'action',
          action: {
            type: 'message',
            label: '📍 協會地址',
            text: '協會地址位置'
          }
        },
        {
          type: 'action',
          action: {
            type: 'message',
            label: '⏰ 服務時間',
            text: '服務時間'
          }
        },
        {
          type: 'action',
          action: {
            type: 'message',
            label: '✉️ 電子郵件',
            text: '協會電子郵件'
          }
        },
        {
          type: 'action',
          action: {
            type: 'message',
            label: '🏠 返回主選單',
            text: '主選單'
          }
        }
      ]
    }
  };

  return quickReplies[type] || quickReplies.main;
}

/**
 * 發送帶有 Quick Reply 的訊息
 * @param {string} replyToken - Line reply token
 * @param {string} message - 要發送的訊息內容
 * @param {string} quickReplyType - Quick Reply 類型
 */
function replyMessageWithQuickReply(replyToken, message, quickReplyType = 'main') {
  try {
    console.log(`Sending reply with quick reply: "${message}" with token: ${replyToken}`);

    if (!CONFIG.CHANNEL_ACCESS_TOKEN) {
      throw new Error('CHANNEL_ACCESS_TOKEN not configured');
    }

    if (!replyToken) {
      throw new Error('Reply token is missing');
    }

    if (!message) {
      throw new Error('Message content is missing');
    }

    // 檢測測試模式
    if (replyToken.includes('test_') || replyToken.includes('mock_')) {
      console.log('⚠️ 檢測到測試模式 - 跳過實際發送');
      console.log('測試回應內容:', message);
      console.log('Quick Reply 類型:', quickReplyType);
      console.log('✅ 測試模式回應成功');
      return true;
    }

    const quickReply = createQuickReply(quickReplyType);

    const payload = {
      replyToken: replyToken,
      messages: [{
        type: 'text',
        text: message,
        quickReply: quickReply
      }]
    };

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${CONFIG.CHANNEL_ACCESS_TOKEN}`
      },
      payload: JSON.stringify(payload)
    };

    console.log('Sending message with quick reply to Line API...');
    const response = UrlFetchApp.fetch(CONFIG.LINE_MESSAGING_API, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();

    console.log(`Line API response: ${responseCode} - ${responseText}`);

    if (responseCode === 200) {
      console.log('Message with quick reply sent successfully');
      return true;
    } else {
      console.error(`Failed to send message: ${responseCode} - ${responseText}`);
      return false;
    }

  } catch (error) {
    console.error('replyMessageWithQuickReply error:', error);
    logError('replyMessageWithQuickReply', error);
    return false;
  }
}

/**
 * 回覆訊息
 * @param {string} replyToken - Reply token
 * @param {string} message - 回覆訊息內容
 */
function replyMessage(replyToken, message) {
  try {
    console.log(`Sending reply message: "${message}" with token: ${replyToken}`);

    if (!CONFIG.CHANNEL_ACCESS_TOKEN) {
      throw new Error('CHANNEL_ACCESS_TOKEN not configured');
    }

    if (!replyToken) {
      throw new Error('Reply token is missing');
    }

    if (!message) {
      throw new Error('Message content is missing');
    }

    // 檢測測試模式（測試用的 reply token）
    if (replyToken.includes('test_') || replyToken.includes('mock_')) {
      console.log('⚠️ 檢測到測試模式 - 跳過實際發送');
      console.log('測試回應內容:', message);
      console.log('✅ 測試模式回應成功');
      return true;
    }

    const payload = {
      replyToken: replyToken,
      messages: [{
        type: 'text',
        text: message
      }]
    };

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${CONFIG.CHANNEL_ACCESS_TOKEN}`
      },
      payload: JSON.stringify(payload)
    };

    console.log('Sending request to Line API...');
    const response = UrlFetchApp.fetch(CONFIG.LINE_MESSAGING_API, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();

    console.log(`Line API response: ${responseCode} - ${responseText}`);

    if (responseCode !== 200) {
      throw new Error(`Line API error: ${responseCode} - ${responseText}`);
    }

    console.log('Message sent successfully');

  } catch (error) {
    console.error('replyMessage error:', error);
    logError('replyMessage', error);
  }
}

/**
 * 推送訊息
 * @param {string} userId - 用戶ID
 * @param {string} message - 推送訊息內容
 */
function pushMessage(userId, message) {
  try {
    const payload = {
      to: userId,
      messages: [{
        type: 'text',
        text: message
      }]
    };
    
    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${CONFIG.CHANNEL_ACCESS_TOKEN}`
      },
      payload: JSON.stringify(payload)
    };
    
    const response = UrlFetchApp.fetch(CONFIG.LINE_PUSH_API, options);
    
    if (response.getResponseCode() !== 200) {
      throw new Error(`Line Push API error: ${response.getResponseCode()}`);
    }
    
  } catch (error) {
    console.error('pushMessage error:', error);
    logError('pushMessage', error);
  }
}

/**
 * 回覆貼圖訊息
 * @param {string} replyToken - Line reply token
 * @param {string} packageId - 貼圖包ID
 * @param {string} stickerId - 貼圖ID
 * @param {string} quickReplyType - Quick Reply 類型
 */
function replyStickerMessage(replyToken, packageId, stickerId, quickReplyType = 'main') {
  try {
    console.log(`Sending sticker reply: packageId=${packageId}, stickerId=${stickerId}`);

    if (!CONFIG.CHANNEL_ACCESS_TOKEN) {
      throw new Error('CHANNEL_ACCESS_TOKEN not configured');
    }

    if (!replyToken) {
      throw new Error('Reply token is missing');
    }

    // 檢測測試模式
    if (replyToken.includes('test_') || replyToken.includes('mock_')) {
      console.log('⚠️ 檢測到測試模式 - 跳過實際發送');
      console.log(`測試貼圖回應: packageId=${packageId}, stickerId=${stickerId}`);
      console.log('✅ 測試模式貼圖回應成功');
      return true;
    }

    const payload = {
      replyToken: replyToken,
      messages: [{
        type: 'sticker',
        packageId: packageId,
        stickerId: stickerId
      }]
    };

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${CONFIG.CHANNEL_ACCESS_TOKEN}`
      },
      payload: JSON.stringify(payload)
    };

    console.log('Sending sticker to Line API...');
    const response = UrlFetchApp.fetch(CONFIG.LINE_MESSAGING_API, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();

    console.log(`Line API response: ${responseCode} - ${responseText}`);

    if (responseCode === 200) {
      console.log('Sticker sent successfully');
      return true;
    } else {
      console.error(`Failed to send sticker: ${responseCode} - ${responseText}`);
      return false;
    }

  } catch (error) {
    console.error('replyStickerMessage error:', error);
    logError('replyStickerMessage', error);
    return false;
  }
}

/**
 * 回覆多重訊息（貼圖+文字）
 * @param {string} replyToken - Line reply token
 * @param {Array} messages - 訊息陣列
 */
function replyMultipleMessages(replyToken, messages) {
  try {
    console.log(`Sending multiple messages: ${messages.length} messages`);

    if (!CONFIG.CHANNEL_ACCESS_TOKEN) {
      throw new Error('CHANNEL_ACCESS_TOKEN not configured');
    }

    if (!replyToken) {
      throw new Error('Reply token is missing');
    }

    if (!messages || messages.length === 0) {
      throw new Error('Messages array is empty');
    }

    // 檢測測試模式
    if (replyToken.includes('test_') || replyToken.includes('mock_')) {
      console.log('⚠️ 檢測到測試模式 - 跳過實際發送');
      console.log('測試多重訊息:', JSON.stringify(messages, null, 2));
      console.log('✅ 測試模式多重訊息回應成功');
      return true;
    }

    const payload = {
      replyToken: replyToken,
      messages: messages
    };

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${CONFIG.CHANNEL_ACCESS_TOKEN}`
      },
      payload: JSON.stringify(payload)
    };

    console.log('Sending multiple messages to Line API...');
    const response = UrlFetchApp.fetch(CONFIG.LINE_MESSAGING_API, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();

    console.log(`Line API response: ${responseCode} - ${responseText}`);

    if (responseCode === 200) {
      console.log('Multiple messages sent successfully');
      return true;
    } else {
      console.error(`Failed to send multiple messages: ${responseCode} - ${responseText}`);
      return false;
    }

  } catch (error) {
    console.error('replyMultipleMessages error:', error);
    logError('replyMultipleMessages', error);
    return false;
  }
}

// ==================== Gemini AI 函數 (Gemini AI Functions) ====================

/**
 * 使用Gemini分析用戶意圖
 * @param {string} userText - 用戶輸入的文字
 * @return {Object} 意圖分析結果
 */
function analyzeUserIntent(userText) {
  try {
    console.log(`Analyzing user intent for: "${userText}"`);

    if (!CONFIG.GEMINI_API_KEY) {
      console.log('Gemini API key not configured, skipping intent analysis');
      return null;
    }

    const prompt = CONFIG.GEMINI_INTENT_PROMPT_TEMPLATE.replace('${userText}', userText);

    const payload = {
      contents: [{
        parts: [{
          text: prompt
        }]
      }],
      generationConfig: {
        temperature: 0.1,
        topK: 1,
        topP: 0.8,
        maxOutputTokens: 1024,
        stopSequences: []
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        }
      ]
    };

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      payload: JSON.stringify(payload)
    };

    const url = `${CONFIG.GEMINI_API_URL}?key=${CONFIG.GEMINI_API_KEY}`;
    console.log('Sending request to Gemini API...');

    const response = UrlFetchApp.fetch(url, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();

    console.log(`Gemini API response: ${responseCode}`);

    if (responseCode !== 200) {
      console.error(`Gemini API error: ${responseCode} - ${responseText}`);
      return null;
    }

    const responseData = JSON.parse(responseText);

    if (!responseData.candidates || !responseData.candidates[0] || !responseData.candidates[0].content) {
      console.error('Invalid Gemini API response structure');
      return null;
    }

    const geminiResponse = responseData.candidates[0].content.parts[0].text;
    console.log(`Gemini raw response: ${geminiResponse}`);

    // 解析JSON回應 - 處理可能的markdown格式
    try {
      let cleanResponse = geminiResponse.trim();

      // 移除可能的markdown代碼塊標記
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      console.log('Cleaned response for parsing:', cleanResponse);

      const intentData = JSON.parse(cleanResponse);
      console.log('Parsed intent data:', JSON.stringify(intentData));
      return intentData;
    } catch (parseError) {
      console.error('Failed to parse Gemini JSON response:', parseError);
      console.error('Raw response:', geminiResponse);

      // 嘗試從回應中提取JSON
      try {
        const jsonMatch = geminiResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          console.log('Attempting to parse extracted JSON:', jsonMatch[0]);
          const intentData = JSON.parse(jsonMatch[0]);
          console.log('Successfully parsed extracted JSON:', JSON.stringify(intentData));
          return intentData;
        }
      } catch (extractError) {
        console.error('Failed to extract and parse JSON:', extractError);
      }

      return null;
    }

  } catch (error) {
    console.error('analyzeUserIntent error:', error);
    logError('analyzeUserIntent', error);
    return null;
  }
}

/**
 * 使用Gemini生成智能回應
 * @param {string} userText - 用戶輸入的文字
 * @param {Object} intentData - 意圖分析結果
 * @return {string} Gemini生成的回應
 */
function generateGeminiResponse(userText, intentData) {
  try {
    console.log(`Generating Gemini response for intent: ${intentData?.intent}`);

    if (!CONFIG.GEMINI_API_KEY) {
      console.log('Gemini API key not configured');
      return null;
    }

    // 根據意圖構建專業的提示詞
    let systemPrompt = `你是雲林光合菌協會的專業客服助理，具備豐富的光合菌知識和協會服務經驗。

請根據以下用戶意圖和問題，提供專業、準確、友善的回應：

用戶意圖: ${intentData?.intent || '未知'}
用戶問題: ${userText}

回應要求：
1. 使用繁體中文回應
2. 語調親切專業
3. 提供準確的光合菌相關資訊
4. 如果涉及產品或服務，引導用戶聯繫協會
5. 回應長度控制在200字以內
6. 如果不確定答案，誠實說明並提供聯繫方式

光合菌協會基本資訊：
- 專精於光合菌技術研發與應用
- 主要產品：農業用光合菌、水產用光合菌、環保用光合菌
- 應用領域：農業、水產養殖、環境治理、健康食品
- 聯絡方式：請輸入「聯絡方式」獲取詳細資訊`;

    const payload = {
      contents: [{
        parts: [{
          text: systemPrompt
        }]
      }],
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.8,
        maxOutputTokens: CONFIG.MAX_OUTPUT_TOKENS,
        stopSequences: []
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        }
      ]
    };

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      payload: JSON.stringify(payload)
    };

    const url = `${CONFIG.GEMINI_API_URL}?key=${CONFIG.GEMINI_API_KEY}`;
    console.log('Sending request to Gemini API for response generation...');

    const response = UrlFetchApp.fetch(url, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();

    console.log(`Gemini response generation API: ${responseCode}`);

    if (responseCode !== 200) {
      console.error(`Gemini API error: ${responseCode} - ${responseText}`);
      return null;
    }

    const responseData = JSON.parse(responseText);

    if (!responseData.candidates || !responseData.candidates[0] || !responseData.candidates[0].content) {
      console.error('Invalid Gemini API response structure');
      return null;
    }

    const geminiResponse = responseData.candidates[0].content.parts[0].text;
    console.log(`Generated Gemini response: ${geminiResponse}`);

    return geminiResponse;

  } catch (error) {
    console.error('generateGeminiResponse error:', error);
    logError('generateGeminiResponse', error);
    return null;
  }
}

// ==================== 知識庫處理函數 (Knowledge Base Functions) ====================

/**
 * 增強的RAG知識庫搜尋
 * @param {string} query - 搜尋關鍵字
 * @param {number} maxResults - 最大結果數量
 * @return {Object|null} 搜尋結果物件或null
 */
function searchKnowledgeBase(query, maxResults = 3) {
  try {
    console.log(`Enhanced RAG search for: "${query}"`);

    if (!CONFIG.KNOWLEDGE_BASE_SHEET_ID) {
      console.log('Knowledge base sheet ID not configured');
      return null;
    }

    const spreadsheet = SpreadsheetApp.openById(CONFIG.KNOWLEDGE_BASE_SHEET_ID);
    const sheet = spreadsheet.getSheetByName('KnowledgeBase') || spreadsheet.getSheets()[0];

    if (!sheet) {
      console.log('No knowledge base sheet found');
      return null;
    }

    const data = sheet.getDataRange().getValues();

    if (data.length <= 1) {
      console.log('Knowledge base is empty');
      return null;
    }

    // 解析標題行
    const headers = data[0];
    const keywordIndex = headers.indexOf('keyword') !== -1 ? headers.indexOf('keyword') : 0;
    const answerIndex = headers.indexOf('answer') !== -1 ? headers.indexOf('answer') : 1;
    const categoryIndex = headers.indexOf('category') !== -1 ? headers.indexOf('category') : 2;
    const tagsIndex = headers.indexOf('tags') !== -1 ? headers.indexOf('tags') : 3;
    const priorityIndex = headers.indexOf('priority') !== -1 ? headers.indexOf('priority') : 4;

    console.log(`Knowledge base structure: keyword(${keywordIndex}), answer(${answerIndex}), category(${categoryIndex}), tags(${tagsIndex}), priority(${priorityIndex})`);

    // 搜尋結果陣列
    const results = [];
    const queryLower = query.toLowerCase();

    // 跳過標題行，搜尋所有行
    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      const keyword = row[keywordIndex] || '';
      const answer = row[answerIndex] || '';
      const category = row[categoryIndex] || '';
      const tags = row[tagsIndex] || '';
      const priority = parseInt(row[priorityIndex]) || 0;

      if (!keyword || !answer) continue;

      // 計算相關性分數
      let relevanceScore = 0;
      const keywordLower = keyword.toLowerCase();
      const tagsLower = tags.toLowerCase();
      const categoryLower = category.toLowerCase();

      // 完全匹配 (最高分)
      if (queryLower === keywordLower) {
        relevanceScore += 100;
      }
      // 關鍵字包含查詢
      else if (keywordLower.includes(queryLower)) {
        relevanceScore += 80;
      }
      // 查詢包含關鍵字
      else if (queryLower.includes(keywordLower)) {
        relevanceScore += 60;
      }

      // 標籤匹配
      if (tags && tagsLower.includes(queryLower)) {
        relevanceScore += 40;
      }

      // 分類匹配
      if (category && categoryLower.includes(queryLower)) {
        relevanceScore += 30;
      }

      // 模糊匹配 (部分字詞)
      const queryWords = queryLower.split(/\s+/);
      const keywordWords = keywordLower.split(/\s+/);

      queryWords.forEach(qWord => {
        if (qWord.length > 1) {
          keywordWords.forEach(kWord => {
            if (kWord.includes(qWord) || qWord.includes(kWord)) {
              relevanceScore += 10;
            }
          });

          // 檢查標籤中的字詞
          if (tagsLower.includes(qWord)) {
            relevanceScore += 15;
          }
        }
      });

      // 優先級加分
      relevanceScore += priority * 5;

      // 如果有相關性，加入結果
      if (relevanceScore > 0) {
        results.push({
          keyword,
          answer,
          category,
          tags,
          priority,
          relevanceScore,
          rowIndex: i + 1
        });
      }
    }

    // 按相關性分數排序
    results.sort((a, b) => b.relevanceScore - a.relevanceScore);

    console.log(`Found ${results.length} relevant results`);

    if (results.length === 0) {
      console.log(`No match found for query: "${query}"`);
      return null;
    }

    // 取前幾個最相關的結果
    const topResults = results.slice(0, maxResults);

    console.log('Top results:', topResults.map(r => `${r.keyword} (score: ${r.relevanceScore})`));

    // 如果最高分數很高，直接返回最佳答案
    if (topResults[0].relevanceScore >= 60) {
      console.log(`High confidence match found: "${topResults[0].keyword}"`);
      return {
        type: 'single',
        answer: topResults[0].answer,
        keyword: topResults[0].keyword,
        category: topResults[0].category,
        confidence: 'high',
        score: topResults[0].relevanceScore
      };
    }

    // 如果有多個相關結果，返回組合答案
    if (topResults.length > 1) {
      console.log(`Multiple relevant results found, combining answers`);
      return {
        type: 'multiple',
        results: topResults,
        confidence: 'medium',
        combinedAnswer: combineKnowledgeResults(topResults, query)
      };
    }

    // 單一結果但信心度較低
    return {
      type: 'single',
      answer: topResults[0].answer,
      keyword: topResults[0].keyword,
      category: topResults[0].category,
      confidence: 'low',
      score: topResults[0].relevanceScore
    };

  } catch (error) {
    console.error('searchKnowledgeBase error:', error);
    logError('searchKnowledgeBase', error);
    return null;
  }
}

/**
 * 組合多個知識庫結果
 * @param {Array} results - 搜尋結果陣列
 * @param {string} query - 原始查詢
 * @return {string} 組合後的答案
 */
function combineKnowledgeResults(results, query) {
  try {
    if (!results || results.length === 0) return '';

    if (results.length === 1) {
      return results[0].answer;
    }

    // 按分類組織結果
    const categories = {};
    results.forEach(result => {
      const category = result.category || '一般資訊';
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(result);
    });

    let combinedAnswer = `關於「${query}」，我找到以下相關資訊：\n\n`;

    // 如果只有一個分類，直接列出答案
    if (Object.keys(categories).length === 1) {
      const categoryResults = Object.values(categories)[0];
      categoryResults.forEach((result, index) => {
        combinedAnswer += `${index + 1}. ${result.answer}\n\n`;
      });
    } else {
      // 多個分類，按分類組織
      Object.keys(categories).forEach(category => {
        combinedAnswer += `【${category}】\n`;
        categories[category].forEach(result => {
          combinedAnswer += `• ${result.answer}\n`;
        });
        combinedAnswer += '\n';
      });
    }

    combinedAnswer += '如需更詳細的資訊，請告訴我您想了解哪個方面。';

    return combinedAnswer;

  } catch (error) {
    console.error('combineKnowledgeResults error:', error);
    return results[0]?.answer || '';
  }
}

/**
 * 關鍵字匹配
 * @param {string} text - 輸入文字
 * @return {string|null} 匹配結果或null
 */
function matchKeywords(text) {
  // 光合菌相關關鍵字
  const photosynthetic_keywords = ['光合菌', '光合細菌', 'psb', '紫色細菌'];
  const application_keywords = ['應用', '用途', '效果', '功能'];
  const product_keywords = ['產品', '購買', '價格', '訂購'];
  const contact_keywords = ['聯絡', '聯繫', '電話', '地址', '客服'];

  if (photosynthetic_keywords.some(keyword => text.includes(keyword))) {
    if (application_keywords.some(keyword => text.includes(keyword))) {
      return getPhotosynthetic_ApplicationInfo();
    }
    return getPhotosynthetic_BasicInfo();
  }

  if (product_keywords.some(keyword => text.includes(keyword))) {
    return getProductInfo();
  }

  if (contact_keywords.some(keyword => text.includes(keyword))) {
    return getContactMessage();
  }

  return null;
}

/**
 * 獲取光合菌基礎資訊
 * @return {string} 光合菌基礎資訊
 */
function getPhotosynthetic_BasicInfo() {
  return `🦠 光合菌基礎資訊\n\n光合菌（Photosynthetic Bacteria）是一群能夠進行光合作用的細菌，主要包括紫色細菌和綠色細菌。\n\n主要特性：\n• 能夠利用光能進行新陳代謝\n• 具有豐富的營養成分\n• 對環境友善，無毒無害\n• 具有多種生物活性物質\n\n如需了解更多應用資訊，請輸入「光合菌應用」\n如需產品諮詢，請輸入「產品資訊」`;
}

/**
 * 獲取光合菌應用資訊
 * @return {string} 光合菌應用資訊
 */
function getPhotosynthetic_ApplicationInfo() {
  return `🌱 光合菌應用領域\n\n1. 農業應用：\n• 土壤改良劑\n• 植物生長促進劑\n• 有機肥料添加劑\n\n2. 水產養殖：\n• 水質淨化\n• 魚蝦健康促進\n• 飼料添加劑\n\n3. 環境治理：\n• 污水處理\n• 土壤修復\n• 有機廢棄物處理\n\n4. 健康食品：\n• 營養補充品\n• 功能性食品原料\n\n需要詳細了解特定應用，請告訴我您感興趣的領域！`;
}

/**
 * 獲取產品資訊
 * @return {string} 產品資訊
 */
function getProductInfo() {
  return `🛍️ 雲林光合菌協會產品資訊\n\n主要產品系列：\n\n1. 農業用光合菌\n• 液態光合菌肥料\n• 光合菌土壤改良劑\n• 有機複合肥料\n\n2. 水產用光合菌\n• 水質調節劑\n• 魚蝦保健品\n• 飼料添加劑\n\n3. 環保用光合菌\n• 污水處理菌劑\n• 堆肥發酵劑\n• 除臭淨化劑\n\n📞 產品諮詢與訂購：\n請聯繫我們的專業團隊\n輸入「聯絡方式」獲取詳細聯繫資訊`;
}

/**
 * 獲取聯絡資訊
 * @return {string} 聯絡資訊
 */
function getContactMessage() {
  return `📞 雲林光合菌協會聯絡資訊\n\n🏢 協會地址：\n雲林縣斗六市（詳細地址請來電洽詢）\n\n📱 聯絡電話：\n05-XXXX-XXXX（請填入實際電話）\n\n📧 電子信箱：\<EMAIL>\n\n🕒 服務時間：\n週一至週五 09:00-17:00\n\n💬 Line客服：\n您目前正在使用Line客服系統\n如需人工客服，請在上班時間來電\n\n🌐 官方網站：\nwww.yunlin-psb.org.tw`;
}

/**
 * 獲取關於資訊
 * @return {string} 關於資訊
 */
function getAboutMessage() {
  return `🏢 關於雲林光合菌協會\n\n雲林光合菌協會致力於推廣光合菌技術的應用與發展，為農業、水產、環保等領域提供專業的光合菌產品與技術服務。\n\n🎯 我們的使命：\n• 推廣環保生物技術\n• 提供優質光合菌產品\n• 促進永續農業發展\n• 改善環境品質\n\n🔬 專業領域：\n• 光合菌培養技術\n• 生物肥料研發\n• 環境生物處理\n• 技術諮詢服務\n\n加入我們，一起為環境永續努力！`;
}

/**
 * 獲取幫助資訊
 * @return {string} 幫助資訊
 */
function getHelpMessage() {
  return `❓ 客服系統使用說明\n\n您可以詢問以下類型的問題：\n\n🦠 光合菌相關：\n• 光合菌是什麼？\n• 光合菌的應用\n• 光合菌的效果\n\n🛍️ 產品相關：\n• 產品資訊\n• 價格諮詢\n• 訂購方式\n\n📞 聯絡相關：\n• 聯絡方式\n• 地址資訊\n• 服務時間\n\n💡 系統指令：\n/help - 顯示此幫助訊息\n/about - 關於協會\n/contact - 聯絡資訊\n/version - 系統版本\n\n直接輸入您的問題，我會盡力為您解答！`;
}

/**
 * 取得關於協會的訊息
 * @return {string} 關於協會的內容
 */
function getAboutMessage() {
  return `🏢 關於雲林光合菌協會

雲林光合菌協會致力於推廣光合菌技術的應用與發展，為農業、水產、環保等領域提供專業服務。

🎯 協會宗旨：
• 推廣環保生物技術
• 提供優質光合菌產品
• 促進永續農業發展
• 改善環境品質
• 培育專業技術人才

🔬 專業領域：
• 光合菌技術研發
• 產品品質控制
• 應用技術指導
• 教育訓練服務

📞 如需更多資訊，請輸入「聯絡方式」
🛍️ 了解產品資訊，請輸入「產品資訊」`;
}

/**
 * 獲取預設回應
 * @return {string} 預設回應
 */
function getDefaultResponse() {
  return `感謝您的詢問！\n\n我是雲林光合菌協會的客服助手，很樂意為您服務。\n\n如果我沒有理解您的問題，請嘗試：\n• 使用更具體的關鍵字\n• 輸入 /help 查看可詢問的問題類型\n• 或直接聯繫我們的人工客服\n\n常見問題：\n🦠 光合菌相關知識\n🛍️ 產品資訊與訂購\n📞 聯絡方式與服務\n\n請告訴我您想了解什麼？`;
}

// ==================== 資料庫處理函數 (Database Functions) ====================

/**
 * 記錄對話內容
 * @param {Object} event - Line event object
 */
function logConversation(event) {
  try {
    console.log('開始記錄對話...');

    if (!CONFIG.CONVERSATION_LOG_SHEET_ID) {
      console.error('CONVERSATION_LOG_SHEET_ID 未設定');
      return;
    }

    console.log('使用 Sheet ID:', CONFIG.CONVERSATION_LOG_SHEET_ID);

    const spreadsheet = SpreadsheetApp.openById(CONFIG.CONVERSATION_LOG_SHEET_ID);
    console.log('成功開啟試算表:', spreadsheet.getName());

    let sheet = spreadsheet.getSheetByName('ConversationLog');

    // 如果 ConversationLog 工作表不存在，嘗試使用其他名稱或建立
    if (!sheet) {
      console.log('ConversationLog 工作表不存在，嘗試其他名稱...');
      sheet = spreadsheet.getSheetByName('對話記錄');

      if (!sheet) {
        console.log('對話記錄 工作表也不存在，建立新的 ConversationLog 工作表...');
        sheet = spreadsheet.insertSheet('ConversationLog');

        // 設定標題行
        const headers = ['timestamp', 'user_id', 'event_type', 'message_content', 'full_event'];
        sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
        sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
        sheet.getRange(1, 1, 1, headers.length).setBackground('#E8F0FE');
        console.log('✅ ConversationLog 工作表已建立並設定標題行');
      }
    }

    console.log('使用工作表:', sheet.getName());
    console.log('當前工作表行數:', sheet.getLastRow());

    const timestamp = new Date();
    const { type, source, message } = event;

    // 確保 source 和 source.userId 存在
    if (!source || !source.userId) {
      console.error('事件缺少必要的 source 或 userId 資訊');
      console.error('事件內容:', JSON.stringify(event));
      return;
    }

    const logData = [
      timestamp,
      source.userId,
      type,
      message ? (message.text || message.type || '') : '',
      JSON.stringify(event)
    ];

    console.log('準備記錄的資料:', logData);

    sheet.appendRow(logData);
    const newRowCount = sheet.getLastRow();
    console.log('✅ 對話記錄成功，新的行數:', newRowCount);

    // 驗證記錄是否成功
    if (newRowCount > 0) {
      const lastRow = sheet.getRange(newRowCount, 1, 1, logData.length).getValues()[0];
      console.log('記錄的資料:', lastRow);
    }

  } catch (error) {
    console.error('logConversation error:', error);
    console.error('錯誤詳情:', error.message);
    console.error('錯誤堆疊:', error.stack);
    logError('logConversation', error);
  }
}

/**
 * 記錄新用戶
 * @param {string} userId - 用戶ID
 */
function logNewUser(userId) {
  try {
    if (!CONFIG.CONVERSATION_LOG_SHEET_ID) {
      return;
    }

    const spreadsheet = SpreadsheetApp.openById(CONFIG.CONVERSATION_LOG_SHEET_ID);
    let sheet = spreadsheet.getSheetByName('Users');

    if (!sheet) {
      console.log('Users sheet not found, creating...');
      sheet = spreadsheet.insertSheet('Users');
      // 設定標題行
      sheet.getRange(1, 1, 1, 4).setValues([['timestamp', 'user_id', 'action', 'notes']]);
    }

    const timestamp = new Date();
    const logData = [
      timestamp,
      userId,
      'follow',
      'New user followed'
    ];

    sheet.appendRow(logData);
    console.log('New user logged successfully');

  } catch (error) {
    console.error('logNewUser error:', error);
    logError('logNewUser', error);
  }
}

/**
 * 記錄用戶取消關注
 * @param {string} userId - 用戶ID
 */
function logUserUnfollow(userId) {
  try {
    if (!CONFIG.CONVERSATION_LOG_SHEET_ID) {
      return;
    }

    const sheet = SpreadsheetApp.openById(CONFIG.CONVERSATION_LOG_SHEET_ID).getSheetByName('Users') ||
                  SpreadsheetApp.openById(CONFIG.CONVERSATION_LOG_SHEET_ID).insertSheet('Users');

    const timestamp = new Date();
    const logData = [
      timestamp,
      userId,
      'unfollow',
      '用戶取消關注'
    ];

    sheet.appendRow(logData);

  } catch (error) {
    console.error('logUserUnfollow error:', error);
  }
}

/**
 * 記錄錯誤
 * @param {string} function_name - 函數名稱
 * @param {Error} error - 錯誤對象
 */
function logError(function_name, error) {
  try {
    if (!CONFIG.CONVERSATION_LOG_SHEET_ID) {
      return;
    }

    const spreadsheet = SpreadsheetApp.openById(CONFIG.CONVERSATION_LOG_SHEET_ID);
    let sheet = spreadsheet.getSheetByName('Errors');

    if (!sheet) {
      console.log('Errors sheet not found, creating...');
      sheet = spreadsheet.insertSheet('Errors');
      // 設定標題行
      sheet.getRange(1, 1, 1, 5).setValues([['timestamp', 'function_name', 'error_message', 'error_stack', 'version']]);
    }

    const timestamp = new Date();
    const logData = [
      timestamp,
      function_name,
      error.message,
      error.stack || '',
      CONFIG.VERSION
    ];

    sheet.appendRow(logData);
    console.log('Error logged successfully');

  } catch (logError) {
    console.error('logError function error:', logError);
  }
}

// ==================== 工具函數 (Utility Functions) ====================

/**
 * 處理Postback資料
 * @param {string} data - Postback data
 * @param {string} userId - 用戶ID
 * @return {string} 回應訊息
 */
function processPostbackData(data, userId) {
  try {
    const parsedData = JSON.parse(data);
    const { action, value } = parsedData;

    switch (action) {
      case 'get_info':
        return getInfoByCategory(value);
      case 'contact':
        return getContactMessage();
      case 'product':
        return getProductInfo();
      default:
        return '抱歉，無法處理此請求。';
    }

  } catch (error) {
    console.error('processPostbackData error:', error);
    return '處理請求時發生錯誤。';
  }
}

/**
 * 根據分類獲取資訊
 * @param {string} category - 分類
 * @return {string} 資訊內容
 */
function getInfoByCategory(category) {
  switch (category) {
    case 'basic':
      return getPhotosynthetic_BasicInfo();
    case 'application':
      return getPhotosynthetic_ApplicationInfo();
    case 'product':
      return getProductInfo();
    case 'contact':
      return getContactMessage();
    default:
      return getDefaultResponse();
  }
}

/**
 * 初始化系統設定
 * 此函數用於首次部署時設置必要的配置
 */
function initializeSystem() {
  try {
    console.log('開始初始化系統...');

    // 檢查必要的配置
    const requiredProperties = [
      'CHANNEL_ACCESS_TOKEN',
      'CHANNEL_SECRET',
      'KNOWLEDGE_BASE_SHEET_ID',
      'CONVERSATION_LOG_SHEET_ID'
    ];

    const properties = PropertiesService.getScriptProperties();
    const missingProperties = [];

    requiredProperties.forEach(prop => {
      if (!properties.getProperty(prop)) {
        missingProperties.push(prop);
      }
    });

    if (missingProperties.length > 0) {
      console.log('缺少以下必要配置：', missingProperties);
      return false;
    }

    // 自動檢查和修正工作表結構
    const sheetsValidation = validateAndFixSheets();
    if (!sheetsValidation) {
      console.log('工作表結構驗證失敗');
      return false;
    }

    console.log('系統初始化完成');
    return true;

  } catch (error) {
    console.error('initializeSystem error:', error);
    return false;
  }
}

/**
 * 測試系統功能
 * 用於驗證系統各項功能是否正常運作
 */
function testSystem() {
  try {
    console.log('開始系統測試...');

    // 測試配置
    const configTest = initializeSystem();
    console.log('配置測試：', configTest ? '通過' : '失敗');

    // 測試工作表結構
    const sheetsTest = validateAndFixSheets();
    console.log('工作表結構測試：', sheetsTest ? '通過' : '失敗');

    // 測試知識庫搜尋
    const knowledgeTest = searchKnowledgeBase('光合菌');
    console.log('知識庫測試：', knowledgeTest ? '通過' : '失敗');

    // 測試關鍵字匹配
    const keywordTest = matchKeywords('光合菌是什麼');
    console.log('關鍵字測試：', keywordTest ? '通過' : '失敗');

    console.log('系統測試完成');

    // 回傳測試結果摘要
    const testResults = {
      配置測試: configTest,
      工作表結構測試: sheetsTest,
      知識庫測試: !!knowledgeTest,
      關鍵字測試: !!keywordTest
    };

    console.log('測試結果摘要：', testResults);
    return testResults;

  } catch (error) {
    console.error('testSystem error:', error);
    return false;
  }
}

// ==================== Gemini AI 測試函數 (Gemini AI Testing Functions) ====================

/**
 * 檢查所有專案屬性設定
 */
function checkAllProperties() {
  try {
    console.log('=== 專案屬性完整檢查 ===');

    const properties = PropertiesService.getScriptProperties();
    const allProperties = properties.getProperties();

    console.log('所有已設定的屬性：');
    Object.keys(allProperties).forEach(key => {
      const value = allProperties[key];
      const maskedValue = key.includes('TOKEN') || key.includes('SECRET') || key.includes('KEY')
        ? value ? `${value.substring(0, 10)}...` : '未設定'
        : value || '未設定';
      console.log(`  ${key}: ${maskedValue}`);
    });

    console.log('\n=== 必要屬性檢查 ===');

    // 檢查Line相關屬性
    const requiredProperties = [
      'CHANNEL_ACCESS_TOKEN',
      'CHANNEL_SECRET',
      'KNOWLEDGE_BASE_SHEET_ID',
      'CONVERSATION_LOG_SHEET_ID'
    ];

    let allRequired = true;
    requiredProperties.forEach(prop => {
      const value = properties.getProperty(prop);
      const status = value ? '✅ 已設定' : '❌ 未設定';
      console.log(`${prop}: ${status}`);
      if (!value) allRequired = false;
    });

    // 檢查可選屬性
    console.log('\n=== 可選屬性檢查 ===');
    const optionalProperties = ['GEMINI_API_KEY'];
    optionalProperties.forEach(prop => {
      const value = properties.getProperty(prop);
      const status = value ? '✅ 已設定' : '⚠️ 未設定 (可選)';
      console.log(`${prop}: ${status}`);
    });

    console.log('\n=== CONFIG 物件檢查 ===');
    console.log('CHANNEL_ACCESS_TOKEN:', CONFIG.CHANNEL_ACCESS_TOKEN ? '已載入' : '❌ 未載入');
    console.log('CHANNEL_SECRET:', CONFIG.CHANNEL_SECRET ? '已載入' : '❌ 未載入');
    console.log('KNOWLEDGE_BASE_SHEET_ID:', CONFIG.KNOWLEDGE_BASE_SHEET_ID ? '已載入' : '❌ 未載入');
    console.log('CONVERSATION_LOG_SHEET_ID:', CONFIG.CONVERSATION_LOG_SHEET_ID ? '已載入' : '❌ 未載入');
    console.log('GEMINI_API_KEY:', CONFIG.GEMINI_API_KEY ? '已載入' : '⚠️ 未載入 (可選)');

    console.log('\n=== 設定建議 ===');
    if (!allRequired) {
      console.log('❌ 缺少必要屬性，Line BOT 無法正常運作');
      console.log('請在 Google Apps Script 專案設定中新增以下屬性：');
      console.log('1. 點擊左側「專案設定」（齒輪圖示）');
      console.log('2. 滾動到「指令碼屬性」區段');
      console.log('3. 點擊「新增指令碼屬性」');
      console.log('4. 依序新增缺少的屬性');
    } else {
      console.log('✅ 所有必要屬性已正確設定');
    }

    return allRequired;

  } catch (error) {
    console.error('checkAllProperties error:', error);
    return false;
  }
}

/**
 * 檢查 Gemini API 配置
 */
function checkGeminiConfig() {
  try {
    console.log('=== Gemini API 配置檢查 ===');

    const apiKey = PropertiesService.getScriptProperties().getProperty('GEMINI_API_KEY');
    console.log('Gemini API Key:', apiKey ? '已設定' : '未設定');

    if (!apiKey) {
      console.log('❌ 請在專案設定中新增 GEMINI_API_KEY 屬性');
      console.log('設定步驟：');
      console.log('1. 前往 https://aistudio.google.com/ 取得 API Key');
      console.log('2. 在 GAS 專案設定 → 指令碼屬性 → 新增屬性');
      console.log('3. 屬性名稱: GEMINI_API_KEY');
      console.log('4. 屬性值: [您的 Gemini API Key]');
      return false;
    }

    console.log('✅ Gemini API Key 已正確設定');
    console.log('API URL:', CONFIG.GEMINI_API_URL);
    console.log('Max Output Tokens:', CONFIG.MAX_OUTPUT_TOKENS);

    return true;

  } catch (error) {
    console.error('checkGeminiConfig error:', error);
    return false;
  }
}

/**
 * 測試 Gemini AI 整合功能
 */
function testGeminiIntegration() {
  try {
    console.log('=== Gemini AI 整合測試 ===');

    // 1. 檢查配置
    const configOK = checkGeminiConfig();
    if (!configOK) {
      console.log('❌ 配置檢查失敗，無法進行AI測試');
      return false;
    }

    // 2. 測試意圖分析
    console.log('\n--- 意圖分析測試 ---');
    const testTexts = [
      '光合菌是什麼？',
      '產品資訊',
      '聯絡方式',
      '關於協會',
      '你好'
    ];

    testTexts.forEach(text => {
      console.log(`測試文字: "${text}"`);
      const result = analyzeUserIntent(text);
      if (result) {
        console.log(`✅ 意圖: ${result.intent}`);
        if (result.parameters) {
          console.log(`   參數: ${JSON.stringify(result.parameters)}`);
        }
      } else {
        console.log('❌ 意圖分析失敗');
      }
      console.log('---');
    });

    // 3. 測試回應生成
    console.log('\n--- 回應生成測試 ---');
    const response = generateGeminiResponse('光合菌的用途', {intent: 'APPLICATION_INQUIRY'});
    if (response) {
      console.log('✅ 回應生成成功');
      console.log('生成的回應:', response);
    } else {
      console.log('❌ 回應生成失敗');
    }

    console.log('\n=== 測試完成 ===');
    return true;

  } catch (error) {
    console.error('testGeminiIntegration error:', error);
    return false;
  }
}

/**
 * 完整的 AI 功能測試
 */
function fullAITest() {
  try {
    console.log('=== 完整 AI 功能測試 ===');

    // 1. 配置檢查
    console.log('1. 檢查配置...');
    const configResult = checkGeminiConfig();
    console.log('配置檢查:', configResult ? '通過' : '失敗');

    if (!configResult) {
      console.log('❌ 配置檢查失敗，停止測試');
      return false;
    }

    // 2. 意圖分析測試
    console.log('\n2. 意圖分析測試...');
    const intentTests = [
      {text: '光合菌是什麼？', expected: 'KNOWLEDGE_INQUIRY'},
      {text: '產品價格', expected: 'PRODUCT_INQUIRY'},
      {text: '聯絡電話', expected: 'CONTACT_INFO'},
      {text: '關於協會', expected: 'ABOUT_ASSOCIATION'}
    ];

    let intentPassCount = 0;
    intentTests.forEach(test => {
      const result = analyzeUserIntent(test.text);
      const passed = result && result.intent === test.expected;
      console.log(`"${test.text}" → ${result?.intent || '無法識別'} (預期: ${test.expected}) ${passed ? '✅' : '❌'}`);
      if (passed) intentPassCount++;
    });

    console.log(`意圖分析通過率: ${intentPassCount}/${intentTests.length} (${Math.round(intentPassCount/intentTests.length*100)}%)`);

    // 3. 回應生成測試
    console.log('\n3. 回應生成測試...');
    const responseTests = [
      {text: '光合菌的用途', intent: 'APPLICATION_INQUIRY'},
      {text: '產品介紹', intent: 'PRODUCT_INQUIRY'},
      {text: '你好', intent: 'CHITCHAT'}
    ];

    let responsePassCount = 0;
    responseTests.forEach(test => {
      const response = generateGeminiResponse(test.text, {intent: test.intent});
      const passed = response && response.length > 0;
      console.log(`"${test.text}" (${test.intent}) → ${passed ? '✅ 成功' : '❌ 失敗'}`);
      if (passed) {
        console.log(`   回應長度: ${response.length} 字元`);
        responsePassCount++;
      }
    });

    console.log(`回應生成通過率: ${responsePassCount}/${responseTests.length} (${Math.round(responsePassCount/responseTests.length*100)}%)`);

    // 4. 整體評估
    console.log('\n=== 測試總結 ===');
    const overallPass = intentPassCount >= intentTests.length * 0.8 && responsePassCount >= responseTests.length * 0.8;
    console.log(`整體評估: ${overallPass ? '✅ 通過' : '❌ 需要改進'}`);

    if (!overallPass) {
      console.log('建議檢查：');
      console.log('1. Gemini API Key 是否正確');
      console.log('2. 網路連接是否正常');
      console.log('3. API 配額是否足夠');
    }

    return overallPass;

  } catch (error) {
    console.error('fullAITest error:', error);
    return false;
  }
}

/**
 * 測試 showTyping 功能
 */
function testShowTyping() {
  try {
    console.log('=== showTyping 功能測試 ===');

    // 使用測試用戶ID
    const testUserId = 'test_user_123';

    console.log(`測試用戶ID: ${testUserId}`);
    console.log('發送 typing 動畫...');

    showTyping(testUserId);

    console.log('✅ showTyping 函數執行完成');
    console.log('注意：實際效果需要在真實的Line對話中測試');

    return true;

  } catch (error) {
    console.error('testShowTyping error:', error);
    return false;
  }
}

/**
 * 測試對話記錄功能
 */
function testConversationLogging() {
  try {
    console.log('=== 對話記錄功能測試 ===');

    // 1. 檢查配置
    console.log('1. 檢查配置...');
    if (!CONFIG.CONVERSATION_LOG_SHEET_ID) {
      console.error('❌ CONVERSATION_LOG_SHEET_ID 未設定');
      return false;
    }
    console.log('✅ CONVERSATION_LOG_SHEET_ID 已設定:', CONFIG.CONVERSATION_LOG_SHEET_ID);

    // 2. 檢查工作表存在性
    console.log('2. 檢查工作表...');
    const spreadsheet = SpreadsheetApp.openById(CONFIG.CONVERSATION_LOG_SHEET_ID);
    console.log('✅ 成功開啟試算表:', spreadsheet.getName());

    let sheet = spreadsheet.getSheetByName('ConversationLog');
    if (!sheet) {
      console.log('ConversationLog 工作表不存在，嘗試其他名稱...');
      sheet = spreadsheet.getSheetByName('對話記錄');
      if (!sheet) {
        console.log('對話記錄 工作表也不存在，使用第一個工作表...');
        sheet = spreadsheet.getSheets()[0];
      }
    }

    if (!sheet) {
      console.error('❌ 找不到任何可用的工作表');
      return false;
    }

    console.log('✅ 使用工作表:', sheet.getName());
    console.log('工作表行數:', sheet.getLastRow());
    console.log('工作表列數:', sheet.getLastColumn());

    // 3. 檢查工作表標題
    if (sheet.getLastRow() > 0) {
      const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
      console.log('工作表標題:', headers);
    } else {
      console.log('工作表為空，將建立標題行');
    }

    // 4. 測試記錄功能
    console.log('3. 測試記錄功能...');
    const testEvent = {
      type: 'message',
      source: {
        userId: 'test_user_12345'
      },
      message: {
        type: 'text',
        text: '測試對話記錄功能'
      },
      replyToken: 'test_reply_token',
      timestamp: Date.now()
    };

    console.log('測試事件:', JSON.stringify(testEvent));

    // 記錄測試對話
    logConversation(testEvent);

    // 5. 驗證記錄結果
    console.log('4. 驗證記錄結果...');
    const newRowCount = sheet.getLastRow();
    console.log('記錄後工作表行數:', newRowCount);

    if (newRowCount > 0) {
      const lastRow = sheet.getRange(newRowCount, 1, 1, sheet.getLastColumn()).getValues()[0];
      console.log('最後一行資料:', lastRow);
      console.log('✅ 對話記錄測試成功');
      return true;
    } else {
      console.error('❌ 對話記錄測試失敗 - 沒有新增資料');
      return false;
    }

  } catch (error) {
    console.error('testConversationLogging error:', error);
    logError('testConversationLogging', error);
    return false;
  }
}

/**
 * 測試Line BOT連接和配置
 */
function testLineBotConnection() {
  try {
    console.log('=== Line BOT 連接測試 ===');

    // 1. 檢查必要配置
    console.log('1. 檢查Line配置...');
    if (!CONFIG.CHANNEL_ACCESS_TOKEN) {
      console.error('❌ CHANNEL_ACCESS_TOKEN 未設定');
      return false;
    }
    if (!CONFIG.CHANNEL_SECRET) {
      console.error('❌ CHANNEL_SECRET 未設定');
      return false;
    }
    console.log('✅ Line配置已設定');

    // 2. 測試Line API連接
    console.log('2. 測試Line API連接...');
    try {
      // 測試用的假回應（不會真正發送）
      const testPayload = {
        replyToken: 'test_reply_token_12345',
        messages: [{
          type: 'text',
          text: '這是連接測試訊息'
        }]
      };

      const options = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${CONFIG.CHANNEL_ACCESS_TOKEN}`
        },
        payload: JSON.stringify(testPayload)
      };

      console.log('發送測試請求到Line API...');
      const response = UrlFetchApp.fetch(CONFIG.LINE_MESSAGING_API, options);
      const responseCode = response.getResponseCode();
      const responseText = response.getContentText();

      console.log(`Line API 回應: ${responseCode}`);
      console.log(`回應內容: ${responseText}`);

      if (responseCode === 400 && responseText.includes('Invalid reply token')) {
        console.log('✅ Line API 認證成功（reply token 錯誤是預期的）');
        console.log('這證明 CHANNEL_ACCESS_TOKEN 是有效的');
        return true;
      } else if (responseCode === 401) {
        console.error('❌ Line API 認證失敗 - 請檢查 CHANNEL_ACCESS_TOKEN');
        return false;
      } else if (responseCode === 200) {
        console.log('✅ Line API 連接完全正常');
        return true;
      } else {
        console.log(`⚠️ 未預期的回應: ${responseCode} - ${responseText}`);
        return false;
      }

    } catch (apiError) {
      console.error('❌ Line API 連接失敗:', apiError);
      return false;
    }

  } catch (error) {
    console.error('testLineBotConnection error:', error);
    return false;
  }
}

/**
 * 測試Webhook處理
 */
function testWebhookProcessing() {
  try {
    console.log('=== Webhook 處理測試 ===');

    // 模擬一個完整的Line Webhook事件
    const mockEvent = {
      postData: {
        contents: JSON.stringify({
          events: [{
            type: 'message',
            source: {
              userId: 'test_user_webhook_123'
            },
            message: {
              type: 'text',
              text: '測試Webhook處理'
            },
            replyToken: 'test_reply_token_webhook',
            timestamp: Date.now()
          }]
        })
      },
      parameter: {},
      headers: {
        'x-line-signature': 'test_signature'
      }
    };

    console.log('模擬Webhook事件:', JSON.stringify(mockEvent.postData.contents));

    // 測試doPost函數（但跳過簽名驗證）
    console.log('測試事件處理...');

    const events = JSON.parse(mockEvent.postData.contents).events;
    events.forEach(event => {
      console.log('處理事件:', event.type);

      // 直接測試handleEvent（跳過doPost的簽名驗證）
      try {
        // 這裡不會真正發送回應，只測試處理邏輯
        console.log('事件處理開始...');

        if (event.type === 'message') {
          console.log('✅ 訊息事件識別正確');
          console.log('用戶ID:', event.source.userId);
          console.log('訊息內容:', event.message.text);
        }

        console.log('✅ Webhook處理測試通過');

      } catch (handleError) {
        console.error('❌ 事件處理失敗:', handleError);
        return false;
      }
    });

    return true;

  } catch (error) {
    console.error('testWebhookProcessing error:', error);
    return false;
  }
}

/**
 * 測試所有記錄功能
 */
function testAllLoggingFunctions() {
  try {
    console.log('=== 所有記錄功能測試 ===');

    let allPassed = true;

    // 1. 測試對話記錄
    console.log('\n--- 對話記錄測試 ---');
    const conversationTest = testConversationLogging();
    console.log('對話記錄測試:', conversationTest ? '✅ 通過' : '❌ 失敗');
    if (!conversationTest) allPassed = false;

    // 2. 測試用戶記錄
    console.log('\n--- 用戶記錄測試 ---');
    try {
      logNewUser('test_user_67890');
      console.log('✅ 用戶記錄測試通過');
    } catch (error) {
      console.error('❌ 用戶記錄測試失敗:', error);
      allPassed = false;
    }

    // 3. 測試錯誤記錄
    console.log('\n--- 錯誤記錄測試 ---');
    try {
      const testError = new Error('測試錯誤記錄');
      logError('testFunction', testError);
      console.log('✅ 錯誤記錄測試通過');
    } catch (error) {
      console.error('❌ 錯誤記錄測試失敗:', error);
      allPassed = false;
    }

    // 4. 檢查工作表狀態
    console.log('\n--- 工作表狀態檢查 ---');
    displaySheetsInfo();

    console.log('\n=== 測試總結 ===');
    console.log('所有記錄功能測試:', allPassed ? '✅ 通過' : '❌ 失敗');

    return allPassed;

  } catch (error) {
    console.error('testAllLoggingFunctions error:', error);
    return false;
  }
}

/**
 * 檢查 Web App 部署狀態
 */
function checkWebAppDeployment() {
  try {
    console.log('=== Web App 部署狀態檢查 ===');

    // 1. 檢查當前腳本資訊
    console.log('1. 腳本基本資訊');
    const scriptId = ScriptApp.getScriptId();
    console.log('Script ID:', scriptId);

    // 2. 生成 Web App URL
    const webAppUrl = `https://script.google.com/macros/s/${scriptId}/exec`;
    console.log('預期的 Web App URL:', webAppUrl);

    // 3. 檢查 doGet 和 doPost 函數
    console.log('\n2. 檢查 Webhook 處理函數');

    try {
      // 檢查 doPost 函數是否存在
      if (typeof doPost === 'function') {
        console.log('✅ doPost 函數存在');
      } else {
        console.error('❌ doPost 函數不存在');
        return false;
      }

      // 檢查 doGet 函數是否存在
      if (typeof doGet === 'function') {
        console.log('✅ doGet 函數存在');
      } else {
        console.log('⚠️ doGet 函數不存在（可選）');
      }

    } catch (error) {
      console.error('函數檢查失敗:', error);
    }

    // 4. 部署指引
    console.log('\n3. 部署檢查清單');
    console.log('請確認以下步驟已完成：');
    console.log('□ 1. 點擊 GAS 編輯器右上角「部署」按鈕');
    console.log('□ 2. 選擇「新增部署作業」');
    console.log('□ 3. 類型選擇「網頁應用程式」');
    console.log('□ 4. 執行身分：「我」');
    console.log('□ 5. 存取權限：「任何人」');
    console.log('□ 6. 點擊「部署」');
    console.log('□ 7. 複製生成的 Web App URL');

    console.log('\n4. Line Webhook 設定檢查清單');
    console.log('請在 Line Developers Console 確認：');
    console.log('□ 1. 進入您的 Channel → Messaging API');
    console.log('□ 2. 將 Web App URL 設定為 Webhook URL');
    console.log('□ 3. 啟用「Use webhook」');
    console.log('□ 4. 點擊「Verify」測試連接');
    console.log('□ 5. 停用「Auto-reply messages」');
    console.log('□ 6. 停用「Greeting messages」');

    console.log('\n✅ Web App 部署檢查完成');
    console.log('如果按照清單完成設定，Line BOT 應該能正常回應');

    return true;

  } catch (error) {
    console.error('checkWebAppDeployment error:', error);
    return false;
  }
}

/**
 * 測試 doGet 函數（用於驗證 Web App 是否正常運作）
 */
function testDoGet() {
  try {
    console.log('=== 測試 doGet 函數 ===');

    // 模擬 doGet 調用
    const mockEvent = {
      parameter: {},
      parameters: {}
    };

    console.log('模擬 doGet 調用...');
    const result = doGet(mockEvent);

    if (result) {
      console.log('✅ doGet 函數執行成功');
      console.log('回應類型:', typeof result);

      if (result.getContent) {
        console.log('回應內容:', result.getContent());
      }
    } else {
      console.log('⚠️ doGet 函數沒有回應');
    }

    return true;

  } catch (error) {
    console.error('testDoGet error:', error);
    return false;
  }
}

/**
 * 測試 doPost 函數（用於驗證 Webhook 處理）
 */
function testDoPost() {
  try {
    console.log('=== 測試 doPost 函數 ===');

    // 模擬 Line Webhook 請求
    const mockEvent = {
      postData: {
        contents: JSON.stringify({
          events: [{
            type: 'message',
            source: {
              userId: 'test_user_dopost_123'
            },
            message: {
              type: 'text',
              text: '測試doPost函數'
            },
            replyToken: 'test_reply_token_dopost',
            timestamp: Date.now()
          }]
        }),
        headers: {}
      },
      parameter: {}
    };

    console.log('模擬 doPost 調用...');
    console.log('模擬請求內容:', mockEvent.postData.contents);

    const result = doPost(mockEvent);

    if (result) {
      console.log('✅ doPost 函數執行成功');
      console.log('回應類型:', typeof result);

      if (result.getContent) {
        console.log('回應內容:', result.getContent());
      }

      if (result.getMimeType) {
        console.log('MIME類型:', result.getMimeType());
      }
    } else {
      console.log('⚠️ doPost 函數沒有回應');
    }

    console.log('⚠️ 注意：測試中的 "Invalid reply token" 錯誤是正常的');
    console.log('這表示系統正確處理了 Webhook 並嘗試回應');
    console.log('在實際使用中，Line 會提供有效的 reply token');

    return true;

  } catch (error) {
    console.error('testDoPost error:', error);
    return false;
  }
}

/**
 * 測試 doPost 函數（僅驗證結構，不執行回應）
 */
function testDoPostStructure() {
  try {
    console.log('=== 測試 doPost 結構（不發送回應）===');

    // 暫時替換 replyMessage 函數以避免實際發送
    const originalReplyMessage = global.replyMessage;
    global.replyMessage = function(replyToken, message) {
      console.log('模擬回應 - Reply Token:', replyToken);
      console.log('模擬回應 - 訊息:', message);
      console.log('✅ 回應函數被正確調用');
      return true;
    };

    // 模擬 Line Webhook 請求
    const mockEvent = {
      postData: {
        contents: JSON.stringify({
          events: [{
            type: 'message',
            source: {
              userId: 'test_user_structure_123'
            },
            message: {
              type: 'text',
              text: '測試結構驗證'
            },
            replyToken: 'mock_reply_token_structure',
            timestamp: Date.now()
          }]
        }),
        headers: {}
      },
      parameter: {}
    };

    console.log('執行結構測試...');
    const result = doPost(mockEvent);

    // 恢復原始函數
    global.replyMessage = originalReplyMessage;

    if (result && result.getContent) {
      console.log('✅ doPost 結構測試成功');
      console.log('回應內容:', result.getContent());
      return true;
    } else {
      console.log('❌ doPost 結構測試失敗');
      return false;
    }

  } catch (error) {
    console.error('testDoPostStructure error:', error);
    return false;
  }
}

/**
 * 安全的 doPost 測試（不會觸發實際回應）
 */
function safeTestDoPost() {
  try {
    console.log('=== 安全的 doPost 測試 ===');

    // 使用測試專用的 reply token
    const mockEvent = {
      postData: {
        contents: JSON.stringify({
          events: [{
            type: 'message',
            source: {
              userId: 'test_user_safe_123'
            },
            message: {
              type: 'text',
              text: '安全測試訊息'
            },
            replyToken: 'test_safe_reply_token',
            timestamp: Date.now()
          }]
        }),
        headers: {}
      },
      parameter: {}
    };

    console.log('執行安全測試...');
    console.log('使用測試 reply token，不會實際發送到 Line API');

    const result = doPost(mockEvent);

    if (result && result.getContent) {
      console.log('✅ 安全測試成功');
      console.log('回應狀態:', result.getContent());
      console.log('✅ doPost 函數運作正常');
      return true;
    } else {
      console.log('❌ 安全測試失敗');
      return false;
    }

  } catch (error) {
    console.error('safeTestDoPost error:', error);
    return false;
  }
}

/**
 * 完整的系統診斷 - Line BOT無回應問題排查
 */
function diagnoseLineBotIssues() {
  try {
    console.log('=== Line BOT 無回應問題完整診斷 ===');
    console.log('診斷時間:', new Date().toLocaleString());

    let issuesFound = [];
    let allPassed = true;

    // 1. 專案屬性檢查
    console.log('\n🔍 步驟 1: 專案屬性檢查');
    const propertiesOK = checkAllProperties();
    if (!propertiesOK) {
      issuesFound.push('專案屬性設定不完整');
      allPassed = false;
    }

    // 2. Line BOT 連接測試
    console.log('\n🔍 步驟 2: Line BOT 連接測試');
    const connectionOK = testLineBotConnection();
    if (!connectionOK) {
      issuesFound.push('Line API 認證失敗');
      allPassed = false;
    }

    // 3. Webhook 處理測試
    console.log('\n🔍 步驟 3: Webhook 處理測試');
    const webhookOK = testWebhookProcessing();
    if (!webhookOK) {
      issuesFound.push('Webhook 處理異常');
      allPassed = false;
    }

    // 4. 工作表功能測試
    console.log('\n🔍 步驟 4: 工作表功能測試');
    const sheetsOK = validateAndFixSheets();
    if (!sheetsOK) {
      issuesFound.push('工作表結構問題');
      allPassed = false;
    }

    // 5. AI 功能測試（如果已設定）
    console.log('\n🔍 步驟 5: AI 功能測試');
    if (CONFIG.GEMINI_API_KEY) {
      const aiOK = checkGeminiConfig();
      if (!aiOK) {
        issuesFound.push('AI 功能配置問題');
        // AI 功能是可選的，不影響基本功能
      }
    } else {
      console.log('⚠️ Gemini API Key 未設定，將使用傳統回應方式');
    }

    // 6. Web App 部署檢查
    console.log('\n🔍 步驟 6: Web App 部署檢查');
    const webAppOK = checkWebAppDeployment();
    if (!webAppOK) {
      issuesFound.push('Web App 部署問題');
      allPassed = false;
    }

    // 7. 生成診斷報告
    console.log('\n📋 診斷報告');
    console.log('='.repeat(50));

    if (allPassed && issuesFound.length === 0) {
      console.log('✅ 系統診斷通過 - 所有核心功能正常');
      console.log('\n如果 Line BOT 仍無回應，請檢查：');
      console.log('1. Line Developers Console 中的 Webhook 設定');
      console.log('2. Web App 部署狀態');
      console.log('3. Line Channel 的基本設定');
    } else {
      console.log('❌ 發現以下問題：');
      issuesFound.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });

      console.log('\n🔧 建議修復步驟：');
      if (issuesFound.includes('專案屬性設定不完整')) {
        console.log('- 執行 checkAllProperties() 查看缺少的屬性');
        console.log('- 在 GAS 專案設定中新增必要的屬性');
      }
      if (issuesFound.includes('Line API 連接失敗')) {
        console.log('- 檢查 CHANNEL_ACCESS_TOKEN 是否正確');
        console.log('- 確認 Line Channel 狀態正常');
      }
      if (issuesFound.includes('Webhook 處理異常')) {
        console.log('- 檢查程式碼邏輯');
        console.log('- 查看 GAS 執行日誌');
      }
    }

    console.log('\n📞 如需進一步協助：');
    console.log('- 技術支援: <EMAIL>');
    console.log('- 提供此診斷報告的完整輸出');

    return allPassed;

  } catch (error) {
    console.error('diagnoseLineBotIssues error:', error);
    return false;
  }
}

// ==================== 工作表驗證與修正函數 (Sheets Validation Functions) ====================

/**
 * 驗證並修正 Google Sheets 結構
 * @return {boolean} 驗證結果
 */
function validateAndFixSheets() {
  try {
    console.log('開始驗證工作表結構...');

    // 定義必要的工作表結構 (使用英文命名)
    const requiredSheets = {
      'KnowledgeBase': {
        name: 'KnowledgeBase',
        displayName: 'Knowledge Base',
        headers: ['keyword', 'answer', 'category'],
        description: 'Photosynthetic bacteria knowledge base',
        sampleData: [
          ['光合菌', '光合菌（Photosynthetic Bacteria）是一群能夠進行光合作用的細菌，主要包括紫色細菌和綠色細菌。它們能夠利用光能進行新陳代謝，具有豐富的營養成分，對環境友善且無毒無害。', '基礎知識'],
          ['應用', '光合菌可應用於農業、水產、環保等領域：1.農業-作為土壤改良劑和植物生長促進劑 2.水產養殖-用於水質淨化和魚蝦健康促進 3.環境治理-污水處理和土壤修復 4.健康食品-營養補充品原料', '應用資訊'],
          ['產品', '雲林光合菌協會主要產品：1.農業用光合菌（液態肥料、土壤改良劑、有機複合肥）2.水產用光合菌（水質調節劑、魚蝦保健品、飼料添加劑）3.環保用光合菌（污水處理菌劑、堆肥發酵劑、除臭淨化劑）', '產品資訊']
        ]
      },
      'ConversationLog': {
        name: 'ConversationLog',
        displayName: 'Conversation Log',
        headers: ['timestamp', 'user_id', 'event_type', 'message_content', 'full_event'],
        description: 'User conversation records',
        sampleData: []
      },
      'Users': {
        name: 'Users',
        displayName: 'User Records',
        headers: ['timestamp', 'user_id', 'action', 'notes'],
        description: 'User behavior records',
        sampleData: []
      },
      'Errors': {
        name: 'Errors',
        displayName: 'Error Log',
        headers: ['timestamp', 'function_name', 'error_message', 'error_stack', 'version'],
        description: 'System error logs',
        sampleData: []
      }
    };

    // 驗證知識庫工作表
    const knowledgeBaseResult = validateAndFixKnowledgeBase(requiredSheets);

    // 驗證對話記錄工作表
    const conversationLogResult = validateAndFixConversationLog(requiredSheets);

    if (knowledgeBaseResult && conversationLogResult) {
      console.log('✅ 所有工作表結構驗證通過');
      return true;
    } else {
      console.log('❌ 工作表結構驗證失敗');
      return false;
    }

  } catch (error) {
    console.error('validateAndFixSheets error:', error);
    return false;
  }
}

/**
 * 驗證並修正知識庫工作表
 * @param {Object} requiredSheets 必要工作表定義
 * @return {boolean} 驗證結果
 */
function validateAndFixKnowledgeBase(requiredSheets) {
  try {
    const sheetId = CONFIG.KNOWLEDGE_BASE_SHEET_ID;
    if (!sheetId) {
      console.log('❌ 知識庫 Sheet ID 未設定');
      return false;
    }

    console.log('驗證知識庫工作表...');
    const spreadsheet = SpreadsheetApp.openById(sheetId);

    // 檢查並修正主要工作表 (KnowledgeBase 或 Sheet1)
    let mainSheet = spreadsheet.getSheetByName('KnowledgeBase') ||
                    spreadsheet.getSheetByName('Sheet1') ||
                    spreadsheet.getSheetByName('知識庫');

    if (!mainSheet) {
      console.log('建立知識庫工作表...');
      mainSheet = spreadsheet.insertSheet('KnowledgeBase');
    } else if (mainSheet.getName() !== 'KnowledgeBase') {
      // 如果工作表存在但名稱不是 KnowledgeBase，重新命名
      console.log(`重新命名工作表 "${mainSheet.getName()}" 為 "KnowledgeBase"`);
      mainSheet.setName('KnowledgeBase');
    }

    // 驗證並修正標題行
    const sheetConfig = requiredSheets['KnowledgeBase'];
    const result = validateAndFixSheetHeaders(mainSheet, sheetConfig);

    if (result) {
      console.log('✅ 知識庫工作表驗證通過');
      return true;
    } else {
      console.log('❌ 知識庫工作表驗證失敗');
      return false;
    }

  } catch (error) {
    console.error('validateAndFixKnowledgeBase error:', error);
    return false;
  }
}

/**
 * 驗證並修正對話記錄工作表
 * @param {Object} requiredSheets 必要工作表定義
 * @return {boolean} 驗證結果
 */
function validateAndFixConversationLog(requiredSheets) {
  try {
    const sheetId = CONFIG.CONVERSATION_LOG_SHEET_ID;
    if (!sheetId) {
      console.log('❌ 對話記錄 Sheet ID 未設定');
      return false;
    }

    console.log('驗證對話記錄相關工作表...');
    const spreadsheet = SpreadsheetApp.openById(sheetId);

    // 需要驗證的工作表
    const sheetsToValidate = ['ConversationLog', 'Users', 'Errors'];
    let allValid = true;

    sheetsToValidate.forEach(sheetName => {
      try {
        let sheet = spreadsheet.getSheetByName(sheetName);

        if (!sheet) {
          console.log(`建立工作表: ${sheetName}`);
          sheet = spreadsheet.insertSheet(sheetName);
        }

        const sheetConfig = requiredSheets[sheetName];
        const result = validateAndFixSheetHeaders(sheet, sheetConfig);

        if (result) {
          console.log(`✅ ${sheetName} 工作表驗證通過`);
        } else {
          console.log(`❌ ${sheetName} 工作表驗證失敗`);
          allValid = false;
        }

      } catch (sheetError) {
        console.error(`處理工作表 ${sheetName} 時發生錯誤:`, sheetError);
        allValid = false;
      }
    });

    return allValid;

  } catch (error) {
    console.error('validateAndFixConversationLog error:', error);
    return false;
  }
}

/**
 * 驗證並修正工作表標題行
 * @param {Sheet} sheet Google Sheets 工作表物件
 * @param {Object} sheetConfig 工作表配置
 * @return {boolean} 驗證結果
 */
function validateAndFixSheetHeaders(sheet, sheetConfig) {
  try {
    const { headers, sampleData, description } = sheetConfig;

    // 檢查工作表是否為空
    const lastRow = sheet.getLastRow();
    const lastCol = sheet.getLastColumn();

    if (lastRow === 0 || lastCol === 0) {
      console.log(`工作表 ${sheet.getName()} 為空，建立標題行...`);

      // 設定標題行
      const headerRange = sheet.getRange(1, 1, 1, headers.length);
      headerRange.setValues([headers]);

      // 設定標題行格式
      headerRange.setFontWeight('bold');
      headerRange.setBackground('#E8F0FE');
      headerRange.setBorder(true, true, true, true, true, true);

      // 如果有範例資料，則新增
      if (sampleData && sampleData.length > 0) {
        console.log(`新增範例資料到 ${sheet.getName()}...`);
        const dataRange = sheet.getRange(2, 1, sampleData.length, headers.length);
        dataRange.setValues(sampleData);
      }

      // 自動調整欄寬
      sheet.autoResizeColumns(1, headers.length);

      console.log(`✅ ${sheet.getName()} 標題行建立完成`);
      return true;
    }

    // 檢查現有標題行是否正確
    const existingHeaders = sheet.getRange(1, 1, 1, headers.length).getValues()[0];
    let headersMatch = true;

    for (let i = 0; i < headers.length; i++) {
      if (existingHeaders[i] !== headers[i]) {
        headersMatch = false;
        break;
      }
    }

    if (!headersMatch) {
      console.log(`修正 ${sheet.getName()} 標題行...`);

      // 修正標題行
      const headerRange = sheet.getRange(1, 1, 1, headers.length);
      headerRange.setValues([headers]);
      headerRange.setFontWeight('bold');
      headerRange.setBackground('#E8F0FE');
      headerRange.setBorder(true, true, true, true, true, true);

      console.log(`✅ ${sheet.getName()} 標題行修正完成`);
    } else {
      console.log(`✅ ${sheet.getName()} 標題行正確`);
    }

    return true;

  } catch (error) {
    console.error(`validateAndFixSheetHeaders error for ${sheet.getName()}:`, error);
    return false;
  }
}

/**
 * 手動執行工作表結構驗證和修正
 * 可在 GAS 編輯器中直接執行此函數
 */
function manualValidateSheets() {
  try {
    console.log('=== 手動執行工作表驗證 ===');

    const result = validateAndFixSheets();

    if (result) {
      console.log('🎉 工作表驗證完成！所有必要的工作表和欄位都已正確設定。');

      // 顯示工作表資訊
      displaySheetsInfo();

    } else {
      console.log('❌ 工作表驗證失敗，請檢查錯誤訊息並重試。');
    }

    return result;

  } catch (error) {
    console.error('manualValidateSheets error:', error);
    return false;
  }
}

/**
 * 顯示工作表資訊
 */
function displaySheetsInfo() {
  try {
    console.log('\n=== 工作表資訊總覽 ===');

    // 顯示知識庫工作表資訊
    if (CONFIG.KNOWLEDGE_BASE_SHEET_ID) {
      const knowledgeSpreadsheet = SpreadsheetApp.openById(CONFIG.KNOWLEDGE_BASE_SHEET_ID);
      console.log(`\n📊 知識庫試算表: ${knowledgeSpreadsheet.getName()}`);
      console.log(`🔗 連結: https://docs.google.com/spreadsheets/d/${CONFIG.KNOWLEDGE_BASE_SHEET_ID}/edit`);

      const sheets = knowledgeSpreadsheet.getSheets();
      sheets.forEach(sheet => {
        const lastRow = sheet.getLastRow();
        const lastCol = sheet.getLastColumn();
        console.log(`   📋 ${sheet.getName()}: ${lastRow}行 x ${lastCol}欄`);
      });
    }

    // 顯示對話記錄工作表資訊
    if (CONFIG.CONVERSATION_LOG_SHEET_ID && CONFIG.CONVERSATION_LOG_SHEET_ID !== CONFIG.KNOWLEDGE_BASE_SHEET_ID) {
      const logSpreadsheet = SpreadsheetApp.openById(CONFIG.CONVERSATION_LOG_SHEET_ID);
      console.log(`\n📝 對話記錄試算表: ${logSpreadsheet.getName()}`);
      console.log(`🔗 連結: https://docs.google.com/spreadsheets/d/${CONFIG.CONVERSATION_LOG_SHEET_ID}/edit`);

      const sheets = logSpreadsheet.getSheets();
      sheets.forEach(sheet => {
        const lastRow = sheet.getLastRow();
        const lastCol = sheet.getLastColumn();
        console.log(`   📋 ${sheet.getName()}: ${lastRow}行 x ${lastCol}欄`);
      });
    }

  } catch (error) {
    console.error('displaySheetsInfo error:', error);
  }
}

/**
 * 重置工作表結構（謹慎使用）
 * 此函數會清除所有工作表內容並重新建立標準結構
 */
function resetSheetsStructure() {
  try {
    console.log('⚠️  警告：即將重置工作表結構，這會清除所有現有資料！');

    // 為了安全起見，需要手動確認
    const confirmation = '請在程式碼中手動設定 RESET_CONFIRMED = true 來確認重置';
    const RESET_CONFIRMED = false; // 手動設定為 true 來執行重置

    if (!RESET_CONFIRMED) {
      console.log('❌ 重置已取消。如要執行重置，請修改程式碼中的 RESET_CONFIRMED 變數。');
      return false;
    }

    console.log('開始重置工作表結構...');

    // 刪除現有工作表並重新建立
    const requiredSheets = ['對話記錄', 'Users', 'Errors'];

    if (CONFIG.CONVERSATION_LOG_SHEET_ID) {
      const spreadsheet = SpreadsheetApp.openById(CONFIG.CONVERSATION_LOG_SHEET_ID);

      requiredSheets.forEach(sheetName => {
        const existingSheet = spreadsheet.getSheetByName(sheetName);
        if (existingSheet) {
          spreadsheet.deleteSheet(existingSheet);
          console.log(`已刪除工作表: ${sheetName}`);
        }
      });
    }

    // 重新驗證和建立工作表
    const result = validateAndFixSheets();

    if (result) {
      console.log('✅ 工作表結構重置完成');
    } else {
      console.log('❌ 工作表結構重置失敗');
    }

    return result;

  } catch (error) {
    console.error('resetSheetsStructure error:', error);
    return false;
  }
}

/**
 * 測試貼圖回應功能
 */
function testStickerResponse() {
  try {
    console.log('=== 測試貼圖回應功能 ===');

    // 測試不同類型的貼圖回應
    const testCases = [
      { packageId: '1', stickerId: '1', description: '開心貼圖' },
      { packageId: '1', stickerId: '13', description: '打招呼貼圖' },
      { packageId: '1', stickerId: '6', description: '愛心貼圖' },
      { packageId: '11537', stickerId: '52002734', description: 'Brown貼圖' },
      { packageId: '999', stickerId: '999', description: '未知貼圖' }
    ];

    testCases.forEach((testCase, index) => {
      console.log(`\n--- 測試案例 ${index + 1}: ${testCase.description} ---`);
      const response = getStickerResponse(testCase.packageId, testCase.stickerId);
      console.log('回應類型:', response.type);
      console.log('回應貼圖:', `${response.packageId}:${response.stickerId}`);
      console.log('回應文字:', response.text);
      console.log('Quick Reply類型:', response.quickReplyType);
    });

    // 測試貼圖訊息處理
    console.log('\n=== 測試貼圖訊息處理 ===');
    const mockStickerMessage = {
      stickerId: '1',
      packageId: '1'
    };

    handleStickerMessage(mockStickerMessage, 'test_reply_token_123', 'test_user_456');

    console.log('✅ 貼圖回應功能測試完成');

  } catch (error) {
    console.error('❌ 貼圖回應功能測試失敗:', error);
  }
}
