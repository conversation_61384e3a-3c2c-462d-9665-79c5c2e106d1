# 🤖 Gemini AI 整合指南

## 🆕 v1.3.0 重大更新

**更新日期**: 2025-01-24  
**版本**: v1.3.0

### ✨ 新增功能

1. **🧠 Gemini AI 智能語意分析**
   - 精準判斷用戶意圖
   - 智能生成專業回應
   - 光合菌領域專業知識整合

2. **⚡ 用戶體驗優化**
   - 收到訊息時優先顯示輸入動畫
   - 智能回應流程優化
   - 多層次備援機制

3. **🎯 專業意圖識別**
   - 光合菌知識諮詢
   - 應用領域諮詢
   - 產品諮詢
   - 技術支援
   - 聯絡資訊
   - 關於協會

## 🔧 設定步驟

### 步驟 1: 取得 Gemini API Key

1. **前往 Google AI Studio**:
   https://aistudio.google.com/

2. **建立 API Key**:
   - 點擊「Get API key」
   - 選擇或建立專案
   - 複製生成的 API Key

3. **在 Google Apps Script 中設定**:
   - 開啟 GAS 編輯器
   - 點擊「專案設定」→「指令碼屬性」
   - 新增屬性：
     ```
     屬性名稱: GEMINI_API_KEY
     屬性值: [您的Gemini API Key]
     ```

### 步驟 2: 驗證設定

在 GAS 編輯器中執行：

```javascript
function testGeminiIntegration() {
  console.log('Gemini API Key:', CONFIG.GEMINI_API_KEY ? '已設定' : '未設定');
  
  if (CONFIG.GEMINI_API_KEY) {
    // 測試意圖分析
    const testResult = analyzeUserIntent('光合菌是什麼？');
    console.log('意圖分析測試結果:', testResult);
  }
}
```

## 🧠 AI 功能詳解

### 意圖分析系統

系統會自動分析用戶輸入，識別以下意圖：

#### 🔬 **專業諮詢類**
1. **KNOWLEDGE_INQUIRY** - 光合菌知識諮詢
   - 觸發詞：光合菌、光合細菌、PSB、紫色細菌、是什麼、原理、特性
   - 範例：「光合菌是什麼？」

2. **APPLICATION_INQUIRY** - 應用領域諮詢
   - 觸發詞：應用、用途、效果、功能、農業、水產、環保
   - 範例：「光合菌有什麼用途？」

3. **PRODUCT_INQUIRY** - 產品諮詢
   - 觸發詞：產品、價格、介紹、購買、訂購、肥料、菌劑
   - 範例：「你們有什麼產品？」

4. **TECHNICAL_SUPPORT** - 技術支援
   - 觸發詞：技術、使用方法、培養、操作、問題、故障
   - 範例：「如何使用光合菌？」

#### 📞 **服務資訊類**
5. **CONTACT_INFO** - 聯絡資訊
   - 觸發詞：聯絡、電話、地址、客服、協會、位置
   - 範例：「聯絡方式是什麼？」

6. **ABOUT_ASSOCIATION** - 關於協會
   - 觸發詞：關於、協會、成立、宗旨、服務、團隊
   - 範例：「介紹一下協會」

#### 🎨 **AI 功能類**
7. **GENERATE_IMAGE** - 生成圖片
   - 觸發詞：畫一張、畫畫、生成圖片、產生一張圖
   - 範例：「畫一張光合菌的圖」

8. **GENERATE_SPEECH** - 生成語音
   - 觸發詞：用說的、唸出來、語音訊息、幫我念
   - 範例：「用說的告訴我光合菌是什麼」

### 智能回應流程

```
用戶訊息 → 顯示輸入動畫 → Gemini意圖分析 → 智能回應生成
    ↓
如果AI失敗 → 傳統知識庫搜尋 → 關鍵字匹配 → 預設回應
```

## 📊 回應品質提升

### AI 回應特色

1. **🎯 精準理解**
   - 深度語意分析
   - 上下文理解
   - 意圖準確識別

2. **📚 專業知識**
   - 光合菌領域專精
   - 協會服務整合
   - 技術術語理解

3. **💬 自然對話**
   - 親切專業語調
   - 繁體中文回應
   - 適當長度控制

4. **🔄 智能備援**
   - AI失敗時自動切換
   - 多層次回應機制
   - 確保服務不中斷

## 🧪 測試與驗證

### 基本功能測試

發送以下測試訊息：

```
1. 光合菌知識測試：
   - "光合菌是什麼？"
   - "光合菌的原理"
   - "PSB是什麼意思？"

2. 應用諮詢測試：
   - "光合菌有什麼用途？"
   - "農業上如何使用？"
   - "水產養殖的效果"

3. 產品諮詢測試：
   - "你們有什麼產品？"
   - "價格怎麼算？"
   - "如何購買？"

4. 閒聊測試：
   - "你好"
   - "今天天氣如何？"
   - "謝謝你的幫助"
```

### 進階功能測試

```javascript
// 在 GAS 編輯器中執行
function advancedAITest() {
  // 測試意圖分析
  const intents = [
    '光合菌是什麼？',
    '產品價格',
    '聯絡電話',
    '關於協會'
  ];
  
  intents.forEach(text => {
    console.log(`測試文字: "${text}"`);
    const result = analyzeUserIntent(text);
    console.log('分析結果:', result);
    console.log('---');
  });
}
```

## 📈 效能監控

### 監控指標

1. **回應時間**
   - AI分析時間：< 3秒
   - 總回應時間：< 5秒

2. **準確率**
   - 意圖識別準確率：> 90%
   - 回應相關性：> 85%

3. **可用性**
   - AI服務可用率：> 95%
   - 備援機制啟動率：< 5%

### 日誌分析

檢查 GAS 執行日誌中的關鍵訊息：

```
✅ 正常流程：
- "Analyzing user intent with Gemini..."
- "Detected intent: KNOWLEDGE_INQUIRY"
- "Found knowledge base response"
- "Message sent successfully"

⚠️ 備援啟動：
- "Gemini API key not configured"
- "AI response failed, falling back to traditional method"
- "Using traditional processing"

❌ 錯誤情況：
- "Gemini API error: 401"
- "Failed to parse Gemini JSON response"
- "All methods failed, using default response"
```

## 🔧 故障排除

### 常見問題

#### 1. AI 功能無法使用

**症狀**: 系統使用傳統方法回應，沒有AI分析

**解決方案**:
```javascript
// 檢查 Gemini API Key 設定
function checkGeminiConfig() {
  const apiKey = PropertiesService.getScriptProperties().getProperty('GEMINI_API_KEY');
  console.log('Gemini API Key:', apiKey ? '已設定' : '未設定');
  
  if (!apiKey) {
    console.log('請在專案設定中新增 GEMINI_API_KEY 屬性');
  }
}
```

#### 2. 意圖識別不準確

**症狀**: AI回應與用戶問題不相關

**解決方案**:
- 檢查意圖分析結果
- 調整意圖判斷模板
- 增加訓練範例

#### 3. 回應時間過長

**症狀**: 用戶等待時間超過10秒

**解決方案**:
- 檢查網路連接
- 監控Gemini API回應時間
- 調整timeout設定

### 手動測試工具

```javascript
// 完整的AI功能測試
function fullAITest() {
  console.log('=== Gemini AI 功能測試 ===');
  
  // 1. 配置檢查
  console.log('1. 檢查配置...');
  checkGeminiConfig();
  
  // 2. 意圖分析測試
  console.log('2. 意圖分析測試...');
  const testTexts = ['光合菌是什麼？', '產品資訊', '聯絡方式'];
  testTexts.forEach(text => {
    const intent = analyzeUserIntent(text);
    console.log(`"${text}" → ${intent?.intent || '無法識別'}`);
  });
  
  // 3. 回應生成測試
  console.log('3. 回應生成測試...');
  const response = generateGeminiResponse('光合菌的用途', {intent: 'APPLICATION_INQUIRY'});
  console.log('生成回應:', response ? '成功' : '失敗');
  
  console.log('=== 測試完成 ===');
}
```

## 🚀 最佳實踐

### 1. API Key 管理
- 定期更換 API Key
- 監控 API 使用量
- 設定適當的配額限制

### 2. 回應品質優化
- 定期檢視AI回應品質
- 收集用戶反饋
- 調整提示詞模板

### 3. 效能優化
- 監控回應時間
- 優化API調用頻率
- 實施快取機制（未來版本）

## 📞 技術支援

如果您在使用Gemini AI功能時遇到問題：

1. **檢查設定**: 執行 `checkGeminiConfig()` 函數
2. **查看日誌**: 檢查 GAS 執行日誌的詳細訊息
3. **測試功能**: 執行 `fullAITest()` 進行完整測試
4. **聯繫支援**: <EMAIL>

---

**🎉 v1.3.0 讓您的Line BOT更加智能！**

整合Gemini AI後，您的客服系統現在具備了真正的人工智能，能夠理解用戶意圖並提供更加精準和自然的回應。
