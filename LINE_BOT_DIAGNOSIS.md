# 🔍 Line BOT 無回應問題診斷指南

## 🚨 問題現象

**症狀**: Line BOT 測試都正常，但實際使用時沒有任何回應

**可能原因**: 專案屬性設定問題、Line API 配置錯誤、Webhook 設定問題

## 🧪 立即診斷步驟

### 步驟 1: 執行完整系統診斷

在 Google Apps Script 編輯器中執行：

```javascript
diagnoseLineBotIssues()
```

這會進行完整的系統檢查，包括：
- ✅ 專案屬性檢查
- ✅ Line BOT 連接測試  
- ✅ Webhook 處理測試
- ✅ 工作表功能測試
- ✅ AI 功能測試

### 步驟 2: 檢查專案屬性設定

執行專項檢查：

```javascript
checkAllProperties()
```

**必要屬性清單**:

| 屬性名稱 | 說明 | 取得方式 |
|---------|------|---------|
| `CHANNEL_ACCESS_TOKEN` | Line Channel 存取權杖 | Line Developers Console |
| `CHANNEL_SECRET` | Line Channel 密鑰 | Line Developers Console |
| `KNOWLEDGE_BASE_SHEET_ID` | 知識庫 Google Sheets ID | Google Sheets URL |
| `CONVERSATION_LOG_SHEET_ID` | 對話記錄 Google Sheets ID | Google Sheets URL |

**可選屬性**:
| 屬性名稱 | 說明 | 取得方式 |
|---------|------|---------|
| `GEMINI_API_KEY` | Gemini AI API 金鑰 | Google AI Studio |

### 步驟 3: 檢查 Line BOT 連接

```javascript
testLineBotConnection()
```

這會測試：
- Line API 認證是否正確
- CHANNEL_ACCESS_TOKEN 是否有效
- API 連接是否正常

## 🔧 常見問題修復

### 問題 1: 專案屬性未設定

**症狀**: `checkAllProperties()` 顯示屬性未設定

**修復步驟**:

1. **開啟 Google Apps Script 編輯器**
2. **點擊左側「專案設定」**（齒輪圖示）
3. **滾動到「指令碼屬性」區段**
4. **點擊「新增指令碼屬性」**
5. **依序新增必要屬性**

#### Line Channel 屬性設定

**取得 CHANNEL_ACCESS_TOKEN 和 CHANNEL_SECRET**:

1. 前往 [Line Developers Console](https://developers.line.biz/)
2. 選擇您的 Provider 和 Channel
3. 在「Basic settings」頁面找到 **Channel secret**
4. 在「Messaging API」頁面找到 **Channel access token**
5. 如果沒有 token，點擊「Issue」生成

#### Google Sheets ID 設定

**取得 Sheet ID**:

從 Google Sheets URL 中提取：
```
https://docs.google.com/spreadsheets/d/[SHEET_ID]/edit
```

例如：
```
URL: https://docs.google.com/spreadsheets/d/1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk/edit
Sheet ID: 1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk
```

### 問題 2: Line API 認證失敗

**症狀**: `testLineBotConnection()` 回傳 401 錯誤

**修復步驟**:

1. **檢查 CHANNEL_ACCESS_TOKEN**:
   - 確認沒有多餘的空格
   - 確認 token 沒有過期
   - 重新生成 token（如果需要）

2. **檢查 Line Channel 狀態**:
   - 確認 Channel 已啟用
   - 確認 Messaging API 已啟用

### 問題 3: Webhook 設定問題

**症狀**: API 連接正常但 BOT 仍無回應

**檢查清單**:

#### Web App 部署
1. **確認 Web App 已部署**:
   - 在 GAS 編輯器點擊「部署」→「新增部署作業」
   - 類型選擇「網頁應用程式」
   - 執行身分：「我」
   - 存取權限：「任何人」

2. **取得 Web App URL**:
   - 部署後會得到一個 URL，類似：
   ```
   https://script.google.com/macros/s/[SCRIPT_ID]/exec
   ```

#### Line Webhook 設定
1. **前往 Line Developers Console**
2. **進入您的 Channel → Messaging API**
3. **設定 Webhook URL**:
   - 將 Web App URL 貼入 Webhook URL 欄位
   - 啟用「Use webhook」
   - 點擊「Verify」測試連接

4. **停用自動回覆**:
   - 在「Messaging API」頁面
   - 將「Auto-reply messages」設為「Disabled」
   - 將「Greeting messages」設為「Disabled」

## 🧪 手動測試步驟

### 1. 測試 Web App 直接存取

在瀏覽器中直接訪問您的 Web App URL：
```
https://script.google.com/macros/s/[YOUR_SCRIPT_ID]/exec
```

**預期結果**: 應該看到「Line BOT is running」或類似訊息

### 2. 測試 Webhook 接收

在 Line Developers Console 中：
1. 進入 Messaging API 頁面
2. 點擊 Webhook URL 旁的「Verify」按鈕
3. 檢查是否顯示「Success」

### 3. 檢查 GAS 執行日誌

1. 在 GAS 編輯器中點擊「執行」頁面
2. 發送訊息給 Line BOT
3. 檢查是否有新的執行記錄
4. 查看日誌內容是否有錯誤

## 📊 診斷結果解讀

### ✅ 正常狀態

```
=== Line BOT 無回應問題完整診斷 ===
🔍 步驟 1: 專案屬性檢查
✅ 所有必要屬性已正確設定

🔍 步驟 2: Line BOT 連接測試  
✅ Line API 連接正常

🔍 步驟 3: Webhook 處理測試
✅ Webhook處理測試通過

📋 診斷報告
✅ 系統診斷通過 - 所有核心功能正常
```

### ❌ 問題狀態

```
❌ 發現以下問題：
1. 專案屬性設定不完整
2. Line API 連接失敗

🔧 建議修復步驟：
- 執行 checkAllProperties() 查看缺少的屬性
- 在 GAS 專案設定中新增必要的屬性
- 檢查 CHANNEL_ACCESS_TOKEN 是否正確
```

## 🔄 完整修復流程

### 步驟 1: 診斷問題
```javascript
diagnoseLineBotIssues()
```

### 步驟 2: 修復屬性設定
根據診斷結果，在 GAS 專案設定中新增缺少的屬性

### 步驟 3: 重新測試
```javascript
checkAllProperties()
testLineBotConnection()
```

### 步驟 4: 檢查 Webhook
確認 Line Developers Console 中的 Webhook 設定

### 步驟 5: 實際測試
發送訊息給 Line BOT，檢查是否有回應

## 📞 技術支援

如果按照指南仍無法解決問題：

### 提供診斷資訊
1. `diagnoseLineBotIssues()` 的完整輸出
2. `checkAllProperties()` 的結果
3. Line Developers Console 的截圖
4. GAS 執行日誌的截圖

### 聯繫方式
- **技術支援**: <EMAIL>
- **緊急支援**: 請在郵件標題註明「Line BOT 緊急問題」

### 常見解決時間
- **屬性設定問題**: 5-10 分鐘
- **Webhook 設定問題**: 10-15 分鐘
- **複雜配置問題**: 30-60 分鐘

---

**🎉 v1.4.1 診斷工具已就緒！**

新的診斷功能包括：
- ✅ 完整的專案屬性檢查
- ✅ Line API 連接測試
- ✅ Webhook 處理驗證
- ✅ 系統健康狀態診斷

**立即執行 `diagnoseLineBotIssues()` 開始診斷！** 🔍
