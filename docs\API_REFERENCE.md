# 雲林光合菌協會 Line BOT API 參考文件

## API 概述

本文件詳細說明雲林光合菌協會Line BOT系統的API結構、函數定義和使用方法。

## 系統架構

```
Line Platform → Webhook → Google Apps Script → Google Sheets
     ↑                                              ↓
User Messages ←←←←←←←← Response Processing ←←←←←← Data Storage
```

## 核心配置 (CONFIG)

### 配置物件結構

```javascript
const CONFIG = {
  CHANNEL_ACCESS_TOKEN: string,      // Line Channel Access Token
  CHANNEL_SECRET: string,            // Line Channel Secret
  KNOWLEDGE_BASE_SHEET_ID: string,   // 知識庫Google Sheets ID
  CONVERSATION_LOG_SHEET_ID: string, // 對話記錄Google Sheets ID
  VERSION: string,                   // 系統版本號
  SYSTEM_NAME: string,               // 系統名稱
  WELCOME_MESSAGE: string,           // 歡迎訊息
  LINE_MESSAGING_API: string,        // Line Messaging API端點
  LINE_PUSH_API: string             // Line Push API端點
}
```

## 主要處理函數

### doPost(e)

**描述**: Line Webhook主要處理函數，接收並處理來自Line平台的事件

**參數**:
- `e` (Object): Google Apps Script event object

**回傳值**: 
- `ContentService.TextOutput`: HTTP回應

**範例**:
```javascript
function doPost(e) {
  // 驗證請求來源
  // 解析事件內容
  // 處理各類事件
  // 回傳處理結果
}
```

### verifySignature(e)

**描述**: 驗證Line Webhook簽名，確保請求來源的安全性

**參數**:
- `e` (Object): Event object containing headers and body

**回傳值**: 
- `boolean`: 驗證結果

**安全性**: 使用HMAC-SHA256演算法驗證請求簽名

### handleEvent(event)

**描述**: 根據事件類型分發處理邏輯

**參數**:
- `event` (Object): Line event object

**支援的事件類型**:
- `message`: 訊息事件
- `follow`: 用戶關注事件
- `unfollow`: 用戶取消關注事件
- `postback`: Postback事件

## 訊息處理函數

### handleMessage(event)

**描述**: 處理用戶發送的訊息

**參數**:
- `event` (Object): Message event object

**處理流程**:
1. 驗證訊息類型
2. 提取文字內容
3. 調用文字處理函數
4. 回傳處理結果

### processTextMessage(text, userId)

**描述**: 處理文字訊息並生成回應

**參數**:
- `text` (string): 用戶輸入的文字
- `userId` (string): 用戶ID

**回傳值**: 
- `string`: 回應訊息內容

**處理邏輯**:
1. 清理和標準化輸入
2. 檢查系統指令
3. 搜尋知識庫
4. 關鍵字匹配
5. 預設回應

### processCommand(command, userId)

**描述**: 處理系統指令

**參數**:
- `command` (string): 指令文字（以/開頭）
- `userId` (string): 用戶ID

**支援指令**:
- `/help`: 顯示幫助資訊
- `/about`: 關於協會資訊
- `/contact`: 聯絡資訊
- `/version`: 系統版本

## Line API 函數

### replyMessage(replyToken, message)

**描述**: 回覆用戶訊息

**參數**:
- `replyToken` (string): Line reply token
- `message` (string): 回覆訊息內容

**API端點**: `https://api.line.me/v2/bot/message/reply`

**HTTP方法**: POST

**標頭**:
```javascript
{
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${CHANNEL_ACCESS_TOKEN}`
}
```

### pushMessage(userId, message)

**描述**: 主動推送訊息給用戶

**參數**:
- `userId` (string): 目標用戶ID
- `message` (string): 推送訊息內容

**API端點**: `https://api.line.me/v2/bot/message/push`

## 知識庫函數

### searchKnowledgeBase(query)

**描述**: 搜尋知識庫並回傳匹配結果

**參數**:
- `query` (string): 搜尋關鍵字

**回傳值**: 
- `string|null`: 搜尋結果或null

**資料來源**: Google Sheets知識庫

**搜尋邏輯**: 關鍵字部分匹配

### matchKeywords(text)

**描述**: 關鍵字匹配處理

**參數**:
- `text` (string): 輸入文字

**回傳值**: 
- `string|null`: 匹配結果或null

**關鍵字分類**:
- 光合菌相關: `['光合菌', '光合細菌', 'psb', '紫色細菌']`
- 應用相關: `['應用', '用途', '效果', '功能']`
- 產品相關: `['產品', '購買', '價格', '訂購']`
- 聯絡相關: `['聯絡', '聯繫', '電話', '地址', '客服']`

## 資料庫函數

### logConversation(event)

**描述**: 記錄對話內容到Google Sheets

**參數**:
- `event` (Object): Line event object

**記錄欄位**:
- 時間戳記
- 用戶ID
- 事件類型
- 訊息內容
- 完整事件JSON

### logNewUser(userId)

**描述**: 記錄新用戶關注

**參數**:
- `userId` (string): 用戶ID

**工作表**: Users

### logError(function_name, error)

**描述**: 記錄系統錯誤

**參數**:
- `function_name` (string): 發生錯誤的函數名稱
- `error` (Error): 錯誤物件

**工作表**: Errors

## 回應內容函數

### getPhotosynthetic_BasicInfo()

**描述**: 取得光合菌基礎資訊

**回傳值**: `string` - 光合菌基礎知識內容

### getPhotosynthetic_ApplicationInfo()

**描述**: 取得光合菌應用資訊

**回傳值**: `string` - 光合菌應用領域介紹

### getProductInfo()

**描述**: 取得產品資訊

**回傳值**: `string` - 協會產品介紹

### getContactMessage()

**描述**: 取得聯絡資訊

**回傳值**: `string` - 協會聯絡方式

### getHelpMessage()

**描述**: 取得幫助資訊

**回傳值**: `string` - 系統使用說明

### getDefaultResponse()

**描述**: 取得預設回應

**回傳值**: `string` - 預設回應內容

## 工具函數

### initializeSystem()

**描述**: 初始化系統設定

**回傳值**: `boolean` - 初始化結果

**檢查項目**:
- 必要配置是否完整
- Google Sheets連接狀態
- Line API設定狀態

### testSystem()

**描述**: 測試系統功能

**測試項目**:
- 配置測試
- 知識庫搜尋測試
- 關鍵字匹配測試

## 錯誤處理

### 錯誤類型

1. **配置錯誤**: 缺少必要的系統配置
2. **API錯誤**: Line API調用失敗
3. **資料庫錯誤**: Google Sheets存取失敗
4. **處理錯誤**: 訊息處理過程中的錯誤

### 錯誤記錄

所有錯誤都會記錄到Google Sheets的Errors工作表，包含：
- 錯誤時間
- 函數名稱
- 錯誤訊息
- 錯誤堆疊
- 系統版本

## 資料格式

### Line Event Object

```javascript
{
  type: string,           // 事件類型
  timestamp: number,      // 時間戳記
  source: {
    type: string,         // 來源類型 (user/group/room)
    userId: string        // 用戶ID
  },
  replyToken: string,     // 回覆Token
  message: {              // 訊息物件 (僅message事件)
    type: string,         // 訊息類型
    text: string          // 訊息內容
  }
}
```

### Knowledge Base Format

```csv
關鍵字,回答,分類
光合菌,光合菌是一群能夠進行光合作用的細菌...,基礎知識
應用,光合菌可應用於農業、水產、環保等領域...,應用資訊
```

## 版本資訊

**當前版本**: v1.0.0
**API版本**: Line Messaging API v2
**平台**: Google Apps Script V8 Runtime

## 限制與注意事項

1. **執行時間限制**: Google Apps Script單次執行最長6分鐘
2. **API配額**: Line Messaging API有月度配額限制
3. **資料庫大小**: Google Sheets單一檔案最大10MB
4. **並發處理**: 建議避免高並發請求

## 安全性考量

1. **簽名驗證**: 所有Webhook請求都必須通過簽名驗證
2. **敏感資料**: Channel Secret等敏感資料使用PropertiesService存儲
3. **存取權限**: Google Sheets設定適當的存取權限
4. **日誌記錄**: 記錄必要的操作日誌，但避免記錄敏感資訊

---

如需更多技術支援，請聯繫開發團隊：<EMAIL>
