# Google Apps Script Clasp 部署指南

## 概述

本指南將協助您使用 Google Apps Script CLI (clasp) 將雲林光合菌協會 Line BOT 專案推送和部署到 Google Apps Script 平台。

## 前置準備

### 1. 安裝 Node.js 和 npm

確保您的系統已安裝 Node.js：

```powershell
# 檢查 Node.js 版本
node --version

# 檢查 npm 版本
npm --version
```

如果尚未安裝，請前往 [Node.js 官網](https://nodejs.org/) 下載並安裝。

### 2. 安裝 Google Apps Script CLI (clasp)

```powershell
# 全域安裝 clasp
npm install -g @google/clasp

# 驗證安裝
clasp --version
```

### 3. 啟用 Google Apps Script API

1. 前往 [Google Apps Script API](https://script.google.com/home/<USER>
2. 開啟「Google Apps Script API」開關

## Clasp 設定

### 1. 登入 Google 帳號

```powershell
# 登入您的 Google 帳號
clasp login
```

這會開啟瀏覽器視窗，請使用您的 Google 帳號登入並授權 clasp。

### 2. 驗證專案配置

檢查 `.clasp.json` 檔案內容：

```json
{
  "scriptId": "1mWN5lA7Nu0m9Do04N12EQ1ECCj75RWQp6nViTHosM27Es8Kt310bcRFI",
  "rootDir": ".",
  "fileExtension": "gs"
}
```

### 3. 檢查忽略檔案

確認 `.claspignore` 檔案正確設定，只推送必要的檔案：
- `code.gs` - 主程式檔案
- `appsscript.json` - GAS 配置檔案

## 部署步驟

### 步驟 1: 檢查當前狀態

```powershell
# 切換到專案目錄
cd "C:\Users\<USER>\OneDrive - MooMoo\光合菌協會\雲林光合菌協會Line BOT"

# 檢查 clasp 狀態
clasp status
```

### 步驟 2: 拉取現有專案 (如果需要)

```powershell
# 如果 GAS 專案中已有檔案，先拉取到本地
clasp pull
```

### 步驟 3: 推送檔案到 GAS

```powershell
# 推送所有檔案到 Google Apps Script
clasp push

# 如果需要強制覆蓋遠端檔案
clasp push --force
```

### 步驟 4: 開啟 GAS 編輯器

```powershell
# 在瀏覽器中開啟 GAS 專案
clasp open
```

### 步驟 5: 部署為 Web App

```powershell
# 建立新的部署版本
clasp deploy --description "雲林光合菌協會Line BOT v1.0.0"

# 查看部署狀態
clasp deployments
```

## PowerShell 指令範例

### 完整部署流程

```powershell
# 1. 切換到專案目錄
Set-Location "C:\Users\<USER>\OneDrive - MooMoo\光合菌協會\雲林光合菌協會Line BOT"

# 2. 檢查 clasp 登入狀態
clasp login --status

# 3. 檢查專案狀態
clasp status

# 4. 推送檔案
clasp push

# 5. 開啟 GAS 編輯器
clasp open

# 6. 部署 Web App
clasp deploy --description "Initial deployment v1.0.0"
```

### 檢查和驗證

```powershell
# 列出專案檔案
clasp list

# 查看專案資訊
clasp project

# 查看部署版本
clasp deployments

# 查看專案版本
clasp versions
```

## 部署後設定

### 1. 在 GAS 編輯器中設定屬性

部署完成後，在 Google Apps Script 編輯器中：

1. 點擊左側「專案設定」（齒輪圖示）
2. 滾動到「指令碼屬性」區段
3. 新增以下屬性：

| 屬性名稱 | 屬性值 |
|---------|-------|
| `KNOWLEDGE_BASE_SHEET_ID` | `1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk` |
| `CONVERSATION_LOG_SHEET_ID` | `1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk` |
| `CHANNEL_ACCESS_TOKEN` | [從 Line Console 取得] |
| `CHANNEL_SECRET` | [從 Line Console 取得] |

### 2. 部署為 Web App

1. 在 GAS 編輯器中點擊「部署」→「新增部署作業」
2. 選擇類型：「網頁應用程式」
3. 設定：
   - 說明：`雲林光合菌協會Line BOT v1.0.0`
   - 執行身分：我
   - 具有存取權的使用者：任何人
4. 點擊「部署」
5. 複製 Web App URL

### 3. 設定 Line Webhook

將取得的 Web App URL 設定到 Line Developers Console 的 Webhook URL。

## 常用 Clasp 指令

### 專案管理

```powershell
# 建立新專案
clasp create --title "專案名稱" --type webapp

# 複製現有專案
clasp clone [scriptId]

# 查看專案資訊
clasp project

# 開啟專案
clasp open
```

### 檔案操作

```powershell
# 推送檔案
clasp push

# 拉取檔案
clasp pull

# 查看檔案狀態
clasp status

# 列出遠端檔案
clasp list
```

### 部署管理

```powershell
# 建立部署
clasp deploy --description "版本說明"

# 查看部署列表
clasp deployments

# 更新現有部署
clasp deploy --deploymentId [deploymentId] --description "更新說明"

# 刪除部署
clasp undeploy [deploymentId]
```

### 版本管理

```powershell
# 查看版本列表
clasp versions

# 建立新版本
clasp version "版本說明"
```

## 故障排除

### 常見問題

#### 1. 登入問題

```powershell
# 重新登入
clasp logout
clasp login
```

#### 2. 權限問題

確保已啟用 Google Apps Script API：
- 前往 https://script.google.com/home/<USER>
- 開啟「Google Apps Script API」

#### 3. 推送失敗

```powershell
# 強制推送
clasp push --force

# 檢查 .claspignore 設定
Get-Content .claspignore
```

#### 4. 專案 ID 錯誤

檢查 `.clasp.json` 中的 `scriptId` 是否正確：
```json
{
  "scriptId": "1mWN5lA7Nu0m9Do04N12EQ1ECCj75RWQp6nViTHosM27Es8Kt310bcRFI"
}
```

### 錯誤訊息處理

| 錯誤訊息 | 解決方案 |
|---------|---------|
| `User has not enabled the Apps Script API` | 啟用 Apps Script API |
| `Permission denied` | 檢查 Google 帳號權限 |
| `Script not found` | 確認 scriptId 正確 |
| `Push failed` | 使用 `--force` 參數 |

## 最佳實踐

### 1. 版本控制

```powershell
# 每次重要更新前建立版本
clasp version "v1.0.1 - 修復知識庫搜尋問題"

# 然後部署
clasp deploy --description "v1.0.1 部署"
```

### 2. 備份策略

```powershell
# 定期拉取最新版本作為備份
clasp pull

# 建立本地備份
Copy-Item code.gs "backup\code_$(Get-Date -Format 'yyyyMMdd').gs"
```

### 3. 測試流程

1. 本地開發和測試
2. 推送到 GAS：`clasp push`
3. 在 GAS 編輯器中測試
4. 建立版本：`clasp version`
5. 部署：`clasp deploy`

## 自動化腳本

創建 PowerShell 自動化部署腳本：

```powershell
# deploy.ps1
Write-Host "開始部署雲林光合菌協會 Line BOT..." -ForegroundColor Green

# 推送檔案
clasp push

if ($LASTEXITCODE -eq 0) {
    Write-Host "檔案推送成功" -ForegroundColor Green
    
    # 建立版本
    $version = Read-Host "請輸入版本說明"
    clasp version $version
    
    # 部署
    clasp deploy --description $version
    
    Write-Host "部署完成！" -ForegroundColor Green
} else {
    Write-Host "推送失敗，請檢查錯誤訊息" -ForegroundColor Red
}
```

---

使用 clasp 可以大幅簡化 Google Apps Script 的開發和部署流程。
按照以上步驟，您就能順利將雲林光合菌協會 Line BOT 部署到 GAS 平台！
