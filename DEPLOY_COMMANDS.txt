雲林光合菌協會 Line BOT 部署指令清單
Yunlin Photosynthetic Bacteria Association Line BOT Deployment Commands

=== 專案資訊 Project Information ===
GAS Project ID: 1mWN5lA7Nu0m9Do04N12EQ1ECCj75RWQp6nViTHosM27Es8Kt310bcRFI
Google Sheets ID: 1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk

=== 快速連結 Quick Links ===
GAS Editor: https://script.google.com/d/1mWN5lA7Nu0m9Do04N12EQ1ECCj75RWQp6nViTHosM27Es8Kt310bcRFI/edit
Google Sheets: https://docs.google.com/spreadsheets/d/1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk/edit
Line Developers: https://developers.line.biz/

=== 部署步驟 Deployment Steps ===

1. 安裝 clasp (如果尚未安裝)
   Install clasp (if not already installed):
   npm install -g @google/clasp

2. 啟用 Google Apps Script API
   Enable Google Apps Script API:
   前往 https://script.google.com/home/<USER>
   開啟 "Google Apps Script API" 開關

3. 登入 Google 帳號
   Login to Google account:
   clasp login

4. 檢查登入狀態
   Check login status:
   clasp login --status

5. 檢查專案狀態
   Check project status:
   clasp status

6. 推送檔案到 GAS
   Push files to GAS:
   clasp push

7. 強制推送 (如果需要)
   Force push (if needed):
   clasp push --force

8. 開啟 GAS 編輯器
   Open GAS editor:
   clasp open

9. 建立版本
   Create version:
   clasp version "v1.0.0 - Initial deployment"

10. 部署 Web App
    Deploy Web App:
    clasp deploy --description "Yunlin PSB Line BOT v1.0.0"

=== 常用指令 Common Commands ===

檢查狀態 Check status:
clasp status

列出檔案 List files:
clasp list

查看部署 View deployments:
clasp deployments

查看版本 View versions:
clasp versions

拉取檔案 Pull files:
clasp pull

=== 部署後設定 Post-deployment Configuration ===

1. 在 GAS 編輯器中設定以下屬性 (專案設定 → 指令碼屬性):
   Set the following properties in GAS editor (Project Settings → Script Properties):

   KNOWLEDGE_BASE_SHEET_ID = 1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk
   CONVERSATION_LOG_SHEET_ID = 1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk
   CHANNEL_ACCESS_TOKEN = [從Line Console取得]
   CHANNEL_SECRET = [從Line Console取得]

2. 在 GAS 編輯器中部署為 Web App:
   Deploy as Web App in GAS editor:
   - 點擊 "部署" → "新增部署作業"
   - 類型: 網頁應用程式
   - 執行身分: 我
   - 存取權: 任何人
   - 複製 Web App URL

3. 設定 Line Webhook:
   Configure Line Webhook:
   - 前往 Line Developers Console
   - 設定 Webhook URL (使用上述 Web App URL)
   - 啟用 Webhook

=== 測試指令 Testing Commands ===

在 GAS 編輯器中執行以下函數:
Execute the following functions in GAS editor:

測試系統初始化:
initializeSystem()

測試系統功能:
testSystem()

=== 故障排除 Troubleshooting ===

問題: clasp 指令無法執行
解決: npm install -g @google/clasp

問題: 未登入錯誤
解決: clasp login

問題: API 未啟用錯誤
解決: 前往 https://script.google.com/home/<USER>

問題: 推送失敗
解決: clasp push --force

問題: 權限錯誤
解決: 確認 Google 帳號有專案編輯權限

=== 檔案說明 File Description ===

推送到 GAS 的檔案:
Files pushed to GAS:
- code.gs (主程式檔案 Main program file)
- appsscript.json (GAS 配置檔案 GAS configuration file)

本地專案檔案:
Local project files:
- .clasp.json (clasp 專案配置)
- .claspignore (忽略檔案清單)
- README.md (專案說明)
- CLASP_DEPLOYMENT.md (詳細部署指南)
- QUICK_START.md (快速開始指南)
- knowledge_base.csv (知識庫範本)

=== 完成 Completion ===

部署完成後，您的 Line BOT 將可以:
After deployment, your Line BOT will be able to:
- 提供 24 小時自動客服
- 回答光合菌相關問題
- 處理產品諮詢
- 記錄對話內容
- 提供協會聯絡資訊

技術支援 Technical Support: <EMAIL>
