# 🔧 Line BOT 故障排除指南

## 🆕 v1.2.0 更新內容

**更新日期**: 2025-01-24  
**版本**: v1.2.0

### ✨ 主要修復和改進

1. **修復Line BOT無回應問題**
   - 加強doPost函數的錯誤處理
   - 改進訊息處理流程
   - 增加詳細的日誌記錄

2. **統一英文命名**
   - 工作表名稱改為英文
   - 欄位名稱統一使用英文
   - 提高系統相容性

3. **增強錯誤處理**
   - 完善的錯誤捕獲機制
   - 詳細的日誌記錄
   - 自動錯誤恢復

## 📊 新的工作表結構

### 工作表名稱對照表

| 舊名稱 | 新名稱 | 說明 |
|--------|--------|------|
| Sheet1/知識庫 | KnowledgeBase | 光合菌知識庫 |
| 對話記錄 | ConversationLog | 用戶對話記錄 |
| Users | Users | 用戶行為記錄 |
| Errors | Errors | 系統錯誤日誌 |

### 欄位名稱對照表

#### KnowledgeBase 工作表
| 舊欄位 | 新欄位 | 說明 |
|--------|--------|------|
| 關鍵字 | keyword | 搜尋關鍵字 |
| 回答 | answer | 回應內容 |
| 分類 | category | 問題分類 |

#### ConversationLog 工作表
| 舊欄位 | 新欄位 | 說明 |
|--------|--------|------|
| 時間戳記 | timestamp | 對話時間 |
| 用戶ID | user_id | Line用戶ID |
| 事件類型 | event_type | 事件類型 |
| 訊息內容 | message_content | 訊息文字 |
| 完整事件 | full_event | 完整事件JSON |

## 🧪 立即測試步驟

### 步驟 1: 執行系統測試

在Google Apps Script編輯器中執行：

```javascript
// 1. 測試系統初始化和工作表結構
manualValidateSheets()

// 2. 執行完整系統測試
testSystem()

// 3. 查看工作表資訊
displaySheetsInfo()
```

### 步驟 2: 測試Line BOT回應

1. **發送測試訊息**：
   ```
   光合菌是什麼？
   /help
   產品資訊
   聯絡方式
   ```

2. **檢查執行日誌**：
   - 在GAS編輯器中查看「執行」日誌
   - 確認每個步驟都有詳細記錄

### 步驟 3: 檢查工作表資料

開啟您的Google Sheets並確認：
- ✅ KnowledgeBase 工作表存在且有資料
- ✅ ConversationLog 工作表記錄對話
- ✅ Users 工作表記錄用戶行為
- ✅ Errors 工作表記錄錯誤（如果有）

## 🔍 常見問題診斷

### 問題 1: Line BOT 完全無回應

**症狀**: 發送訊息給BOT但沒有任何回應

**診斷步驟**:
1. 檢查GAS執行日誌
2. 確認Webhook URL設定正確
3. 驗證Channel Access Token

**解決方案**:
```javascript
// 在GAS編輯器中執行以下測試
function debugWebhook() {
  console.log('Channel Access Token:', CONFIG.CHANNEL_ACCESS_TOKEN ? '已設定' : '未設定');
  console.log('Channel Secret:', CONFIG.CHANNEL_SECRET ? '已設定' : '未設定');
  console.log('Knowledge Base Sheet ID:', CONFIG.KNOWLEDGE_BASE_SHEET_ID);
  console.log('Conversation Log Sheet ID:', CONFIG.CONVERSATION_LOG_SHEET_ID);
}
```

### 問題 2: BOT 回應錯誤訊息

**症狀**: BOT回應「系統暫時發生錯誤」

**診斷步驟**:
1. 檢查Errors工作表
2. 查看GAS執行日誌
3. 確認知識庫格式正確

**解決方案**:
```javascript
// 測試知識庫搜尋
function testKnowledgeBase() {
  const result = searchKnowledgeBase('光合菌');
  console.log('Knowledge base test result:', result);
}
```

### 問題 3: 工作表結構錯誤

**症狀**: 系統無法正確記錄資料

**解決方案**:
```javascript
// 重新驗證和修正工作表結構
manualValidateSheets()
```

### 問題 4: 權限問題

**症狀**: 無法存取Google Sheets

**解決方案**:
1. 確認Google Sheets分享權限
2. 重新授權Google Apps Script
3. 檢查Sheet ID是否正確

## 📋 詳細日誌分析

### 正常運行的日誌範例

```
doPost called - processing webhook request
Request contents: {"events":[{"type":"message",...}]}
Signature verification passed
Parsed request data: {"events":[...]}
Processing event 1: {"type":"message",...}
handleMessage called with event: {...}
Message type: text, text: "光合菌是什麼？", from user: U1234567890
Processing text message: "光合菌是什麼？" from user: U1234567890
Cleaned text: "光合菌是什麼？"
Searching knowledge base...
Found match for "光合菌是什麼？" with keyword "光合菌"
Found knowledge base response
Sending reply message: "光合菌（Photosynthetic Bacteria）..." with token: abc123
Sending request to Line API...
Line API response: 200 - {}
Message sent successfully
Conversation logged successfully
All events processed successfully
```

### 錯誤日誌分析

如果看到以下錯誤：

1. **`CHANNEL_ACCESS_TOKEN not configured`**
   - 解決：在GAS專案屬性中設定正確的Token

2. **`Knowledge base sheet ID not configured`**
   - 解決：確認KNOWLEDGE_BASE_SHEET_ID設定正確

3. **`Line API error: 401`**
   - 解決：檢查Channel Access Token是否正確

4. **`Line API error: 400`**
   - 解決：檢查訊息格式是否正確

## 🛠️ 手動修復步驟

### 重置工作表結構

如果工作表結構完全錯誤，可以執行：

```javascript
// ⚠️ 警告：這會清除所有資料
function resetSheetsStructure() {
  // 需要手動設定 RESET_CONFIRMED = true
  const RESET_CONFIRMED = false; // 改為 true 來執行
  
  if (!RESET_CONFIRMED) {
    console.log('請將 RESET_CONFIRMED 設為 true 來確認重置');
    return;
  }
  
  // 執行重置...
}
```

### 手動建立工作表

如果自動建立失敗，可以手動建立：

1. **KnowledgeBase 工作表**：
   ```
   A1: keyword    B1: answer    C1: category
   A2: 光合菌     B2: 光合菌是...  C2: 基礎知識
   ```

2. **ConversationLog 工作表**：
   ```
   A1: timestamp    B1: user_id    C1: event_type    D1: message_content    E1: full_event
   ```

## 📞 技術支援

### 自助診斷清單

在聯繫技術支援前，請完成以下檢查：

- [ ] 執行 `testSystem()` 並檢查結果
- [ ] 確認所有GAS專案屬性已正確設定
- [ ] 檢查Google Sheets權限設定
- [ ] 驗證Line Webhook URL設定
- [ ] 查看GAS執行日誌和Errors工作表

### 聯繫支援時請提供

1. **錯誤訊息**: 完整的錯誤日誌
2. **系統資訊**: 執行 `displaySheetsInfo()` 的結果
3. **測試結果**: `testSystem()` 的輸出
4. **操作步驟**: 導致問題的具體操作

### 聯繫方式

- **技術支援**: <EMAIL>
- **緊急支援**: 請在郵件標題加上 [緊急]

## 🔄 版本升級注意事項

### 從 v1.1.0 升級到 v1.2.0

1. **自動處理**: 系統會自動重新命名工作表
2. **資料保留**: 現有資料不會丟失
3. **功能增強**: 新增詳細日誌記錄
4. **相容性**: 向下相容舊的工作表名稱

### 升級後檢查清單

- [ ] 執行 `manualValidateSheets()` 驗證工作表
- [ ] 測試Line BOT基本功能
- [ ] 檢查對話記錄是否正常
- [ ] 確認錯誤日誌功能運作

---

**🎉 v1.2.0 讓您的Line BOT更加穩定可靠！**

新版本大幅改善了錯誤處理和日誌記錄，讓問題診斷和解決變得更加容易。
