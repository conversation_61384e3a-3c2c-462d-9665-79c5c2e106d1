# 雲林光合菌協會 Line BOT 簡易部署腳本
# Simple Deployment Script

Write-Host "=== 雲林光合菌協會 Line BOT 部署工具 ===" -ForegroundColor Green

# 檢查 clasp 是否已安裝
Write-Host "檢查 clasp 安裝狀態..." -ForegroundColor Yellow
try {
    $version = clasp --version
    Write-Host "✅ Clasp 已安裝: $version" -ForegroundColor Green
} catch {
    Write-Host "❌ Clasp 未安裝，請執行: npm install -g @google/clasp" -ForegroundColor Red
    exit
}

# 檢查必要檔案
Write-Host "`n檢查必要檔案..." -ForegroundColor Yellow
$files = @("code.gs", "appsscript.json", ".clasp.json")
foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "✅ $file 存在" -ForegroundColor Green
    } else {
        Write-Host "❌ $file 缺失" -ForegroundColor Red
    }
}

# 顯示專案資訊
Write-Host "`n📋 專案資訊:" -ForegroundColor Cyan
Write-Host "GAS專案ID: 1mWN5lA7Nu0m9Do04N12EQ1ECCj75RWQp6nViTHosM27Es8Kt310bcRFI"
Write-Host "Sheets ID: 1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk"

# 提供操作選項
Write-Host "`n🚀 可用操作:" -ForegroundColor Cyan
Write-Host "1. 檢查登入狀態: clasp login --status"
Write-Host "2. 登入 Google: clasp login"
Write-Host "3. 檢查專案狀態: clasp status"
Write-Host "4. 推送檔案: clasp push"
Write-Host "5. 開啟編輯器: clasp open"
Write-Host "6. 部署: clasp deploy --description '部署說明'"

Write-Host "`n📖 詳細指南請參考:" -ForegroundColor Yellow
Write-Host "- CLASP_DEPLOYMENT.md - 完整部署指南"
Write-Host "- QUICK_START.md - 快速開始指南"

Write-Host "`n🔗 快速連結:" -ForegroundColor Cyan
Write-Host "GAS編輯器: https://script.google.com/d/1mWN5lA7Nu0m9Do04N12EQ1ECCj75RWQp6nViTHosM27Es8Kt310bcRFI/edit"
Write-Host "Google Sheets: https://docs.google.com/spreadsheets/d/1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk/edit"

Write-Host "`n準備就緒！請按照上述步驟進行部署。" -ForegroundColor Green
