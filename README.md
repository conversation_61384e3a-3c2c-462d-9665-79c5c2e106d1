# 雲林光合菌協會 Line BOT 客服系統

## 專案概述

本專案為雲林光合菌協會開發的Line BOT客服系統，基於Google Apps Script平台建置，提供24小時自動化客服服務。

### 主要功能

- 🦠 **光合菌專業知識諮詢** - 提供光合菌基礎知識、應用領域等專業資訊
- 🛍️ **產品資訊查詢** - 協會產品介紹、規格說明、訂購資訊
- 📞 **聯絡資訊服務** - 協會聯絡方式、地址、服務時間
- 📊 **對話記錄管理** - 自動記錄用戶對話，便於分析和改進服務
- 🔧 **系統管理功能** - 錯誤記錄、系統監控、配置管理

### 技術架構

- **平台**: Google Apps Script
- **資料庫**: Google Sheets
- **API**: Line Messaging API
- **版本控制**: 語意化版本控制 (MAJOR.MINOR.PATCH)

## 檔案結構

```
雲林光合菌協會Line BOT/
├── code.gs                 # 主要程式碼檔案
├── appsscript.json         # Google Apps Script 配置檔案
├── README.md               # 專案說明文件
├── DEPLOYMENT.md           # 部署說明文件
├── knowledge_base.csv      # 知識庫範本檔案
└── docs/                   # 文件目錄
    ├── API_REFERENCE.md    # API 參考文件
    └── USER_GUIDE.md       # 使用者指南
```

## 🚀 您的專案快速存取

### 專案連結
- **Google Apps Script**: [開啟專案](https://script.google.com/d/1mWN5lA7Nu0m9Do04N12EQ1ECCj75RWQp6nViTHosM27Es8Kt310bcRFI/edit)
- **Google Sheets 資料庫**: [開啟試算表](https://docs.google.com/spreadsheets/d/1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk/edit)
- **Line Developers Console**: [前往設定](https://developers.line.biz/)

### 📋 專案ID資訊
- **GAS專案ID**: `1mWN5lA7Nu0m9Do04N12EQ1ECCj75RWQp6nViTHosM27Es8Kt310bcRFI`
- **Drive ID**: `1kQKlBvCAIMxyeUZctCc69aG75eK6bp0E`
- **Sheets ID**: `1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk`

## 快速開始

### 1. 前置準備

1. **Line Developer Account**
   - 註冊 [Line Developers](https://developers.line.biz/)
   - 建立 Messaging API Channel
   - 取得 Channel Access Token 和 Channel Secret

2. **Google 帳號**
   - 確保有 Google 帳號
   - 可存取 Google Apps Script 和 Google Sheets

### 2. 建立 Google Sheets 資料庫

1. **知識庫 Sheet**
   - 建立新的 Google Sheets
   - 設定欄位：關鍵字 | 回答 | 分類
   - 記錄 Sheet ID

2. **對話記錄 Sheet**
   - 建立另一個 Google Sheets
   - 用於記錄用戶對話和系統日誌
   - 記錄 Sheet ID

### 3. 部署 Google Apps Script

1. 開啟 [Google Apps Script](https://script.google.com/)
2. 建立新專案
3. 複製 `code.gs` 內容到專案中
4. 複製 `appsscript.json` 內容到專案配置
5. 設定專案屬性（詳見部署說明）

### 4. 配置 Line Webhook

1. 部署 Google Apps Script 為 Web App
2. 複製 Web App URL
3. 在 Line Developers Console 設定 Webhook URL
4. 啟用 Webhook

## 配置說明

### 必要配置項目

在 Google Apps Script 的「專案設定」→「指令碼屬性」中設定：

| 屬性名稱 | 說明 | 您的設定值 |
|---------|------|---------|
| `CHANNEL_ACCESS_TOKEN` | Line Channel Access Token | `[從Line Console取得]` |
| `CHANNEL_SECRET` | Line Channel Secret | `[從Line Console取得]` |
| `KNOWLEDGE_BASE_SHEET_ID` | 知識庫 Google Sheets ID | `1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk` |
| `CONVERSATION_LOG_SHEET_ID` | 對話記錄 Google Sheets ID | `1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk` |

### 知識庫格式

知識庫 Google Sheets 格式：

| 關鍵字 | 回答 | 分類 |
|--------|------|------|
| 光合菌 | 光合菌是一群能夠進行光合作用的細菌... | 基礎知識 |
| 應用 | 光合菌可應用於農業、水產、環保等領域... | 應用資訊 |

## 功能說明

### 支援的指令

- `/help` - 顯示幫助資訊
- `/about` - 關於協會資訊
- `/contact` - 聯絡資訊
- `/version` - 系統版本

### 關鍵字回應

系統會自動識別以下關鍵字並提供相應回應：

- **光合菌相關**: 光合菌、光合細菌、PSB、紫色細菌
- **應用相關**: 應用、用途、效果、功能
- **產品相關**: 產品、購買、價格、訂購
- **聯絡相關**: 聯絡、聯繫、電話、地址、客服

## 版本資訊

### 當前版本: v1.0.0

**發布日期**: 2025-01-24

**主要功能**:
- 基礎 Line BOT 架構
- 光合菌知識庫查詢
- 關鍵字自動回應
- 對話記錄功能
- 錯誤日誌記錄

## 維護與支援

### 系統監控

- 定期檢查錯誤日誌
- 監控對話記錄品質
- 更新知識庫內容

### 常見問題

1. **Webhook 無回應**
   - 檢查 Web App 部署狀態
   - 確認 Line Webhook URL 設定正確
   - 檢查 Google Apps Script 執行權限

2. **知識庫無法查詢**
   - 確認 Google Sheets ID 設定正確
   - 檢查 Sheets 權限設定
   - 驗證知識庫格式

### 技術支援

如需技術支援，請聯繫：
- 開發團隊：<EMAIL>
- 系統管理：<EMAIL>

## 授權條款

本專案採用 MIT 授權條款，詳見 LICENSE 檔案。

---

© 2025 雲林光合菌協會. All rights reserved.
