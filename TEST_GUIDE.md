# 雲林光合菌協會 Line BOT 測試指南

## 測試概述

本文件提供完整的測試流程，確保雲林光合菌協會Line BOT客服系統的各項功能正常運作。

## 測試環境準備

### 1. 系統功能測試

在Google Apps Script中執行以下測試：

#### 測試 1: 系統初始化測試
```javascript
// 在GAS編輯器中執行
function runInitTest() {
  const result = initializeSystem();
  console.log('初始化測試結果:', result);
}
```

#### 測試 2: 系統功能測試
```javascript
// 在GAS編輯器中執行
function runSystemTest() {
  testSystem();
}
```

### 2. 配置驗證測試

確認以下配置項目已正確設定：

- [ ] `CHANNEL_ACCESS_TOKEN` - Line Channel Access Token
- [ ] `CHANNEL_SECRET` - Line Channel Secret  
- [ ] `KNOWLEDGE_BASE_SHEET_ID` - 知識庫Google Sheets ID
- [ ] `CONVERSATION_LOG_SHEET_ID` - 對話記錄Google Sheets ID

## Line BOT 功能測試

### 測試案例 1: 基本對話功能

#### 測試步驟：
1. 使用手機Line App掃描BOT QR Code
2. 加為好友
3. 測試以下對話：

| 測試輸入 | 預期回應 | 測試結果 |
|---------|---------|---------|
| 初次加好友 | 歡迎訊息 | ☐ 通過 ☐ 失敗 |
| `光合菌是什麼？` | 光合菌基礎資訊 | ☐ 通過 ☐ 失敗 |
| `產品資訊` | 產品介紹訊息 | ☐ 通過 ☐ 失敗 |
| `聯絡方式` | 聯絡資訊 | ☐ 通過 ☐ 失敗 |

### 測試案例 2: 系統指令測試

| 指令 | 預期回應 | 測試結果 |
|------|---------|---------|
| `/help` | 幫助資訊 | ☐ 通過 ☐ 失敗 |
| `/about` | 關於協會 | ☐ 通過 ☐ 失敗 |
| `/contact` | 聯絡資訊 | ☐ 通過 ☐ 失敗 |
| `/version` | 系統版本 | ☐ 通過 ☐ 失敗 |

### 測試案例 3: 關鍵字匹配測試

| 關鍵字類型 | 測試輸入 | 預期回應類型 | 測試結果 |
|-----------|---------|-------------|---------|
| 光合菌基礎 | `光合菌` | 基礎知識回應 | ☐ 通過 ☐ 失敗 |
| 應用相關 | `光合菌應用` | 應用資訊回應 | ☐ 通過 ☐ 失敗 |
| 產品相關 | `購買產品` | 產品資訊回應 | ☐ 通過 ☐ 失敗 |
| 聯絡相關 | `客服電話` | 聯絡資訊回應 | ☐ 通過 ☐ 失敗 |

### 測試案例 4: 知識庫搜尋測試

#### 前置條件：
- 知識庫Google Sheets已建立並填入測試資料
- Sheet ID已正確配置

#### 測試步驟：
1. 在知識庫中新增測試條目：
   ```
   關鍵字: 測試
   回答: 這是測試回應
   分類: 測試
   ```

2. 在Line BOT中輸入：`測試`

3. 確認回應：`這是測試回應`

**測試結果**: ☐ 通過 ☐ 失敗

### 測試案例 5: 錯誤處理測試

| 測試情境 | 測試方法 | 預期行為 | 測試結果 |
|---------|---------|---------|---------|
| 無效輸入 | 發送特殊字符 | 預設回應 | ☐ 通過 ☐ 失敗 |
| 空白訊息 | 發送空白 | 預設回應 | ☐ 通過 ☐ 失敗 |
| 長文本 | 發送超長文字 | 正常處理 | ☐ 通過 ☐ 失敗 |
| 圖片訊息 | 發送圖片 | 提示僅支援文字 | ☐ 通過 ☐ 失敗 |

## 資料記錄測試

### 測試案例 6: 對話記錄功能

#### 測試步驟：
1. 與BOT進行對話
2. 檢查對話記錄Google Sheets
3. 確認記錄內容包含：
   - [ ] 時間戳記
   - [ ] 用戶ID
   - [ ] 事件類型
   - [ ] 訊息內容

### 測試案例 7: 用戶行為記錄

#### 測試步驟：
1. 加為好友（follow事件）
2. 檢查Users工作表是否記錄
3. 取消關注（unfollow事件）
4. 檢查是否記錄取消關注

**測試結果**: ☐ 通過 ☐ 失敗

## 效能測試

### 測試案例 8: 回應時間測試

#### 測試方法：
1. 記錄發送訊息時間
2. 記錄收到回應時間
3. 計算回應時間差

#### 效能標準：
- 一般回應：< 3秒
- 知識庫查詢：< 5秒
- 系統指令：< 2秒

| 測試類型 | 回應時間 | 是否符合標準 |
|---------|---------|-------------|
| 一般回應 | ___秒 | ☐ 是 ☐ 否 |
| 知識庫查詢 | ___秒 | ☐ 是 ☐ 否 |
| 系統指令 | ___秒 | ☐ 是 ☐ 否 |

### 測試案例 9: 併發測試

#### 測試方法：
1. 使用多個Line帳號同時發送訊息
2. 確認所有訊息都能正確回應
3. 檢查是否有遺漏或錯誤

**測試結果**: ☐ 通過 ☐ 失敗

## 整合測試

### 測試案例 10: Webhook驗證

#### 測試步驟：
1. 在Line Developers Console點擊「Verify」
2. 確認顯示「Success」
3. 檢查GAS執行日誌無錯誤

**測試結果**: ☐ 通過 ☐ 失敗

### 測試案例 11: Google Sheets整合

#### 測試步驟：
1. 確認知識庫Sheet可正常讀取
2. 確認對話記錄Sheet可正常寫入
3. 檢查權限設定正確

**測試結果**: ☐ 通過 ☐ 失敗

## 用戶體驗測試

### 測試案例 12: 對話流暢度

#### 評估項目：
- [ ] 回應內容相關性高
- [ ] 語言表達自然流暢
- [ ] 資訊提供完整準確
- [ ] 操作指引清楚明確

### 測試案例 13: 介面友善度

#### 評估項目：
- [ ] 歡迎訊息親切友善
- [ ] 幫助資訊容易理解
- [ ] 錯誤提示有建設性
- [ ] 聯絡資訊容易取得

## 安全性測試

### 測試案例 14: 資料安全

#### 檢查項目：
- [ ] 用戶資料適當保護
- [ ] 敏感資訊不外洩
- [ ] 存取權限正確設定
- [ ] 日誌記錄適當

### 測試案例 15: 系統穩定性

#### 測試方法：
1. 連續使用24小時
2. 監控系統運行狀況
3. 檢查錯誤日誌
4. 確認服務可用性

**測試結果**: ☐ 通過 ☐ 失敗

## 測試報告

### 測試總結

| 測試類別 | 通過數量 | 失敗數量 | 通過率 |
|---------|---------|---------|--------|
| 基本功能 | ___/4 | ___/4 | __% |
| 系統指令 | ___/4 | ___/4 | __% |
| 關鍵字匹配 | ___/4 | ___/4 | __% |
| 資料記錄 | ___/2 | ___/2 | __% |
| 效能測試 | ___/2 | ___/2 | __% |
| 整合測試 | ___/2 | ___/2 | __% |
| 總計 | ___/18 | ___/18 | __% |

### 問題記錄

| 問題編號 | 問題描述 | 嚴重程度 | 狀態 | 備註 |
|---------|---------|---------|------|------|
| 001 | | ☐高 ☐中 ☐低 | ☐待修復 ☐已修復 | |
| 002 | | ☐高 ☐中 ☐低 | ☐待修復 ☐已修復 | |
| 003 | | ☐高 ☐中 ☐低 | ☐待修復 ☐已修復 | |

### 改進建議

1. 
2. 
3. 

### 測試結論

☐ 系統可以上線使用
☐ 需要修復問題後再測試
☐ 需要重大調整

---

**測試人員**: ________________
**測試日期**: ________________
**測試版本**: v1.0.0
