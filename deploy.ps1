# 雲林光合菌協會 Line BOT 自動部署腳本
# Automated Deployment Script for Yunlin Photosynthetic Bacteria Association Line BOT

param(
    [string]$Action = "deploy",
    [string]$Message = "Auto deployment",
    [switch]$Force = $false
)

Write-Host "=== 雲林光合菌協會 Line BOT 部署工具 ===" -ForegroundColor Green
Write-Host "專案ID: 1mWN5lA7Nu0m9Do04N12EQ1ECCj75RWQp6nViTHosM27Es8Kt310bcRFI" -ForegroundColor Cyan

# 檢查 clasp 是否已安裝
function Test-ClaspInstalled {
    try {
        $version = clasp --version 2>$null
        if ($version) {
            Write-Host "✅ Clasp 已安裝: $version" -ForegroundColor Green
            return $true
        }
    } catch {
        Write-Host "❌ Clasp 未安裝" -ForegroundColor Red
        Write-Host "請執行: npm install -g @google/clasp" -ForegroundColor Yellow
        return $false
    }
    return $false
}

# 檢查登入狀態
function Test-ClaspLogin {
    try {
        $status = clasp login --status 2>$null
        if ($status -match "logged in") {
            Write-Host "✅ 已登入 Google 帳號" -ForegroundColor Green
            return $true
        }
    } catch {
        Write-Host "❌ 未登入 Google 帳號" -ForegroundColor Red
        Write-Host "請執行: clasp login" -ForegroundColor Yellow
        return $false
    }
    return $false
}

# 檢查專案配置
function Test-ProjectConfig {
    if (Test-Path ".clasp.json") {
        $config = Get-Content ".clasp.json" | ConvertFrom-Json
        if ($config.scriptId -eq "1mWN5lA7Nu0m9Do04N12EQ1ECCj75RWQp6nViTHosM27Es8Kt310bcRFI") {
            Write-Host "✅ 專案配置正確" -ForegroundColor Green
            return $true
        }
    }
    Write-Host "❌ 專案配置錯誤或缺失" -ForegroundColor Red
    return $false
}

# 檢查必要檔案
function Test-RequiredFiles {
    $requiredFiles = @("code.gs", "appsscript.json")
    $allExists = $true
    
    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-Host "✅ $file 存在" -ForegroundColor Green
        } else {
            Write-Host "❌ $file 缺失" -ForegroundColor Red
            $allExists = $false
        }
    }
    
    return $allExists
}

# 執行部署前檢查
function Invoke-PreDeploymentCheck {
    Write-Host "`n🔍 執行部署前檢查..." -ForegroundColor Cyan
    
    $checks = @(
        @{ Name = "Clasp 安裝"; Test = { Test-ClaspInstalled } },
        @{ Name = "Google 登入"; Test = { Test-ClaspLogin } },
        @{ Name = "專案配置"; Test = { Test-ProjectConfig } },
        @{ Name = "必要檔案"; Test = { Test-RequiredFiles } }
    )
    
    $allPassed = $true
    foreach ($check in $checks) {
        Write-Host "檢查 $($check.Name)..." -ForegroundColor Yellow
        if (-not (& $check.Test)) {
            $allPassed = $false
        }
    }
    
    return $allPassed
}

# 推送檔案到 GAS
function Invoke-ClaspPush {
    Write-Host "`n📤 推送檔案到 Google Apps Script..." -ForegroundColor Cyan
    
    try {
        if ($Force) {
            Write-Host "使用強制推送模式..." -ForegroundColor Yellow
            clasp push --force
        } else {
            clasp push
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 檔案推送成功" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ 檔案推送失敗" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ 推送過程發生錯誤: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 建立版本
function New-ClaspVersion {
    param([string]$VersionMessage)
    
    Write-Host "`n📋 建立新版本..." -ForegroundColor Cyan
    
    try {
        clasp version $VersionMessage
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 版本建立成功" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ 版本建立失敗" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ 建立版本時發生錯誤: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 部署 Web App
function Invoke-ClaspDeploy {
    param([string]$DeployMessage)
    
    Write-Host "`n🚀 部署 Web App..." -ForegroundColor Cyan
    
    try {
        clasp deploy --description $DeployMessage
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 部署成功" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ 部署失敗" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ 部署過程發生錯誤: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 顯示專案資訊
function Show-ProjectInfo {
    Write-Host "`n📊 專案資訊:" -ForegroundColor Cyan
    
    try {
        Write-Host "專案狀態:" -ForegroundColor Yellow
        clasp status
        
        Write-Host "`n部署列表:" -ForegroundColor Yellow
        clasp deployments
        
        Write-Host "`n版本列表:" -ForegroundColor Yellow
        clasp versions
    } catch {
        Write-Host "無法取得專案資訊" -ForegroundColor Red
    }
}

# 開啟 GAS 編輯器
function Open-GASEditor {
    Write-Host "`n🌐 開啟 Google Apps Script 編輯器..." -ForegroundColor Cyan
    try {
        clasp open
        Write-Host "✅ 已在瀏覽器中開啟 GAS 編輯器" -ForegroundColor Green
    } catch {
        Write-Host "❌ 無法開啟 GAS 編輯器" -ForegroundColor Red
        Write-Host "請手動前往: https://script.google.com/d/1mWN5lA7Nu0m9Do04N12EQ1ECCj75RWQp6nViTHosM27Es8Kt310bcRFI/edit" -ForegroundColor Yellow
    }
}

# 主要執行邏輯
switch ($Action.ToLower()) {
    "check" {
        Write-Host "執行系統檢查..." -ForegroundColor Yellow
        Invoke-PreDeploymentCheck
    }
    
    "push" {
        Write-Host "執行檔案推送..." -ForegroundColor Yellow
        if (Invoke-PreDeploymentCheck) {
            Invoke-ClaspPush
        }
    }
    
    "deploy" {
        Write-Host "執行完整部署..." -ForegroundColor Yellow
        
        # 執行部署前檢查
        if (-not (Invoke-PreDeploymentCheck)) {
            Write-Host "❌ 部署前檢查失敗，請修正問題後重試" -ForegroundColor Red
            exit 1
        }
        
        # 推送檔案
        if (-not (Invoke-ClaspPush)) {
            Write-Host "❌ 檔案推送失敗，部署中止" -ForegroundColor Red
            exit 1
        }
        
        # 建立版本
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $versionMessage = "$Message - $timestamp"
        if (New-ClaspVersion $versionMessage) {
            # 部署
            if (Invoke-ClaspDeploy $versionMessage) {
                Write-Host "`n🎉 部署完成！" -ForegroundColor Green
                Show-ProjectInfo
                
                # 詢問是否開啟編輯器
                $openEditor = Read-Host "`n是否開啟 Google Apps Script 編輯器？ (y/N)"
                if ($openEditor -eq "y" -or $openEditor -eq "Y") {
                    Open-GASEditor
                }
            }
        }
    }
    
    "info" {
        Write-Host "顯示專案資訊..." -ForegroundColor Yellow
        Show-ProjectInfo
    }
    
    "open" {
        Write-Host "開啟 GAS 編輯器..." -ForegroundColor Yellow
        Open-GASEditor
    }
    
    "setup" {
        Write-Host "執行初始設定..." -ForegroundColor Yellow
        
        # 檢查 clasp 安裝
        if (-not (Test-ClaspInstalled)) {
            Write-Host "請先安裝 clasp: npm install -g @google/clasp" -ForegroundColor Red
            exit 1
        }
        
        # 檢查登入狀態
        if (-not (Test-ClaspLogin)) {
            Write-Host "請先登入: clasp login" -ForegroundColor Red
            exit 1
        }
        
        Write-Host "✅ 設定檢查完成" -ForegroundColor Green
        Write-Host "您現在可以執行: .\deploy.ps1 -Action deploy" -ForegroundColor Yellow
    }
    
    default {
        Write-Host "使用方式:" -ForegroundColor Yellow
        Write-Host "  .\deploy.ps1 -Action check     # 檢查系統狀態" -ForegroundColor White
        Write-Host "  .\deploy.ps1 -Action setup     # 初始設定檢查" -ForegroundColor White
        Write-Host "  .\deploy.ps1 -Action push      # 僅推送檔案" -ForegroundColor White
        Write-Host "  .\deploy.ps1 -Action deploy    # 完整部署" -ForegroundColor White
        Write-Host "  .\deploy.ps1 -Action info      # 顯示專案資訊" -ForegroundColor White
        Write-Host "  .\deploy.ps1 -Action open      # 開啟 GAS 編輯器" -ForegroundColor White
        Write-Host "`n參數:" -ForegroundColor Yellow
        Write-Host "  -Message '訊息'    # 自訂部署訊息" -ForegroundColor White
        Write-Host "  -Force             # 強制推送" -ForegroundColor White
    }
}

Write-Host "`n部署工具執行完成。" -ForegroundColor Green
