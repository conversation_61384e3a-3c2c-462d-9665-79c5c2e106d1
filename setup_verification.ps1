# 雲林光合菌協會 Line BOT 設定驗證腳本
# PowerShell Script for Configuration Verification

Write-Host "=== 雲林光合菌協會 Line BOT 設定驗證 ===" -ForegroundColor Green
Write-Host "開始驗證專案配置..." -ForegroundColor Yellow

# 專案資訊
$GAS_PROJECT_ID = "1mWN5lA7Nu0m9Do04N12EQ1ECCj75RWQp6nViTHosM27Es8Kt310bcRFI"
$DRIVE_ID = "1kQKlBvCAIMxyeUZctCc69aG75eK6bp0E"
$SPREADSHEET_ID = "1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk"

Write-Host "`n📋 您的專案資訊：" -ForegroundColor Cyan
Write-Host "Google Apps Script ID: $GAS_PROJECT_ID"
Write-Host "Google Drive ID: $DRIVE_ID"
Write-Host "Google Sheets ID: $SPREADSHEET_ID"

# 檢查本地檔案
Write-Host "`n📁 檢查本地專案檔案..." -ForegroundColor Cyan

$requiredFiles = @(
    "code.gs",
    "appsscript.json",
    "README.md",
    "DEPLOYMENT.md",
    "knowledge_base.csv",
    "CONFIGURATION_SETUP.md"
)

$missingFiles = @()
$existingFiles = @()

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        $existingFiles += $file
        Write-Host "✅ $file - 存在" -ForegroundColor Green
    } else {
        $missingFiles += $file
        Write-Host "❌ $file - 缺失" -ForegroundColor Red
    }
}

# 檢查docs目錄
Write-Host "`n📚 檢查文件目錄..." -ForegroundColor Cyan
if (Test-Path "docs") {
    Write-Host "✅ docs 目錄存在" -ForegroundColor Green
    
    $docFiles = @("API_REFERENCE.md", "USER_GUIDE.md")
    foreach ($docFile in $docFiles) {
        $docPath = "docs\$docFile"
        if (Test-Path $docPath) {
            Write-Host "✅ $docPath - 存在" -ForegroundColor Green
        } else {
            Write-Host "❌ $docPath - 缺失" -ForegroundColor Red
            $missingFiles += $docPath
        }
    }
} else {
    Write-Host "❌ docs 目錄不存在" -ForegroundColor Red
    $missingFiles += "docs目錄"
}

# 檢查code.gs檔案內容
Write-Host "`n🔍 檢查主程式檔案..." -ForegroundColor Cyan
if (Test-Path "code.gs") {
    $codeContent = Get-Content "code.gs" -Raw
    
    # 檢查版本資訊
    if ($codeContent -match "v1\.0\.0") {
        Write-Host "✅ 版本資訊正確 (v1.0.0)" -ForegroundColor Green
    } else {
        Write-Host "⚠️  版本資訊可能需要確認" -ForegroundColor Yellow
    }
    
    # 檢查關鍵函數
    $keyFunctions = @("doPost", "handleMessage", "searchKnowledgeBase", "replyMessage")
    foreach ($func in $keyFunctions) {
        if ($codeContent -match "function $func") {
            Write-Host "✅ 函數 $func 存在" -ForegroundColor Green
        } else {
            Write-Host "❌ 函數 $func 缺失" -ForegroundColor Red
        }
    }
    
    # 檢查配置變數
    if ($codeContent -match "CONFIG\s*=") {
        Write-Host "✅ CONFIG 配置物件存在" -ForegroundColor Green
    } else {
        Write-Host "❌ CONFIG 配置物件缺失" -ForegroundColor Red
    }
    
    # 統計程式碼行數
    $lineCount = (Get-Content "code.gs").Count
    Write-Host "📊 程式碼總行數: $lineCount" -ForegroundColor Blue
}

# 檢查知識庫檔案
Write-Host "`n📖 檢查知識庫檔案..." -ForegroundColor Cyan
if (Test-Path "knowledge_base.csv") {
    $csvContent = Get-Content "knowledge_base.csv"
    $entryCount = $csvContent.Count - 1  # 扣除標題行
    Write-Host "✅ 知識庫條目數量: $entryCount" -ForegroundColor Green
    
    # 檢查CSV格式
    if ($csvContent[0] -match "關鍵字,回答,分類") {
        Write-Host "✅ CSV格式正確" -ForegroundColor Green
    } else {
        Write-Host "⚠️  CSV格式可能需要檢查" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ knowledge_base.csv 檔案缺失" -ForegroundColor Red
}

# 生成設定檢查清單
Write-Host "`n📋 設定檢查清單：" -ForegroundColor Cyan
Write-Host "請確認以下項目已完成：" -ForegroundColor Yellow

$checklistItems = @(
    "已建立Line Developer帳號",
    "已建立Messaging API Channel",
    "已取得Channel Access Token",
    "已取得Channel Secret",
    "已設定Google Apps Script專案屬性",
    "已部署Web App",
    "已設定Line Webhook URL",
    "已驗證Webhook連接"
)

foreach ($item in $checklistItems) {
    Write-Host "☐ $item" -ForegroundColor White
}

# 生成快速連結
Write-Host "`n🔗 快速存取連結：" -ForegroundColor Cyan
Write-Host "Google Apps Script 專案："
Write-Host "https://script.google.com/d/$GAS_PROJECT_ID/edit" -ForegroundColor Blue

Write-Host "`nGoogle Sheets 資料庫："
Write-Host "https://docs.google.com/spreadsheets/d/$SPREADSHEET_ID/edit" -ForegroundColor Blue

Write-Host "`nLine Developers Console："
Write-Host "https://developers.line.biz/" -ForegroundColor Blue

# 總結報告
Write-Host "`n📊 驗證總結：" -ForegroundColor Cyan
Write-Host "存在檔案: $($existingFiles.Count)" -ForegroundColor Green
if ($missingFiles.Count -gt 0) {
    Write-Host "缺失檔案: $($missingFiles.Count)" -ForegroundColor Red
    Write-Host "缺失項目: $($missingFiles -join ', ')" -ForegroundColor Red
} else {
    Write-Host "所有必要檔案都存在！" -ForegroundColor Green
}

# 下一步建議
Write-Host "`n🚀 下一步建議：" -ForegroundColor Cyan

if ($missingFiles.Count -eq 0) {
    Write-Host "1. 開啟 CONFIGURATION_SETUP.md 按照步驟進行配置" -ForegroundColor Green
    Write-Host "2. 設定Line Developer Console" -ForegroundColor Green
    Write-Host "3. 配置Google Apps Script屬性" -ForegroundColor Green
    Write-Host "4. 部署並測試系統" -ForegroundColor Green
} else {
    Write-Host "1. 請先確保所有必要檔案都存在" -ForegroundColor Yellow
    Write-Host "2. 檢查缺失的檔案並補齊" -ForegroundColor Yellow
    Write-Host "3. 重新執行此驗證腳本" -ForegroundColor Yellow
}

# 建立備份
Write-Host "`n💾 建立備份..." -ForegroundColor Cyan
$backupDir = "backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"

try {
    New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
    
    if (Test-Path "code.gs") {
        Copy-Item "code.gs" "$backupDir\" -Force
        Write-Host "✅ code.gs 已備份到 $backupDir" -ForegroundColor Green
    }
    
    if (Test-Path "knowledge_base.csv") {
        Copy-Item "knowledge_base.csv" "$backupDir\" -Force
        Write-Host "✅ knowledge_base.csv 已備份到 $backupDir" -ForegroundColor Green
    }
    
    if (Test-Path "appsscript.json") {
        Copy-Item "appsscript.json" "$backupDir\" -Force
        Write-Host "✅ appsscript.json 已備份到 $backupDir" -ForegroundColor Green
    }
    
    Write-Host "✅ 備份完成！備份位置: $backupDir" -ForegroundColor Green
    
} catch {
    Write-Host "⚠️  備份過程中發生錯誤: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 生成配置資訊檔案
Write-Host "`n📝 生成配置資訊檔案..." -ForegroundColor Cyan

$configInfo = @"
# 雲林光合菌協會 Line BOT 配置資訊
# 生成時間: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

## 專案ID資訊
Google Apps Script 專案ID: $GAS_PROJECT_ID
Google Drive ID: $DRIVE_ID  
Google Sheets ID: $SPREADSHEET_ID

## 快速存取連結
Google Apps Script: https://script.google.com/d/$GAS_PROJECT_ID/edit
Google Sheets: https://docs.google.com/spreadsheets/d/$SPREADSHEET_ID/edit
Line Developers: https://developers.line.biz/

## Google Apps Script 屬性設定
請在 Google Apps Script 的「專案設定」→「指令碼屬性」中設定：

KNOWLEDGE_BASE_SHEET_ID = $SPREADSHEET_ID
CONVERSATION_LOG_SHEET_ID = $SPREADSHEET_ID
CHANNEL_ACCESS_TOKEN = [從Line Console取得]
CHANNEL_SECRET = [從Line Console取得]

## 注意事項
1. 請妥善保管 Channel Access Token 和 Channel Secret
2. 確保 Google Sheets 的分享權限設定正確
3. 部署前請先進行完整測試

"@

$configInfo | Out-File -FilePath "project_config_info.txt" -Encoding UTF8
Write-Host "✅ 配置資訊已儲存到 project_config_info.txt" -ForegroundColor Green

Write-Host "`n🎉 驗證完成！" -ForegroundColor Green
Write-Host "請參考 CONFIGURATION_SETUP.md 進行下一步配置。" -ForegroundColor Yellow

# 暫停以便查看結果
Write-Host "`n按任意鍵繼續..." -ForegroundColor Gray
Read-Host "按 Enter 繼續"
