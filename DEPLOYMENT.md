# 雲林光合菌協會 Line BOT 部署指南

## 部署概述

本文件提供完整的部署步驟，協助您將雲林光合菌協會Line BOT客服系統成功部署到生產環境。

## 部署前準備

### 1. Line Developer Console 設定

#### 步驟 1.1: 建立 Line Developer 帳號
1. 前往 [Line Developers](https://developers.line.biz/)
2. 使用 Line 帳號登入
3. 同意開發者條款

#### 步驟 1.2: 建立 Provider
1. 點擊「Create a new provider」
2. 輸入 Provider 名稱：`雲林光合菌協會`
3. 點擊「Create」

#### 步驟 1.3: 建立 Messaging API Channel
1. 在 Provider 頁面點擊「Create a Messaging API channel」
2. 填寫以下資訊：
   - **Channel name**: `雲林光合菌協會客服BOT`
   - **Channel description**: `提供光合菌相關專業諮詢服務`
   - **Category**: `Science & Technology`
   - **Subcategory**: `Biotechnology`
   - **Email address**: 填入協會聯絡信箱
3. 上傳 Channel icon（建議使用協會 Logo）
4. 同意條款並建立

#### 步驟 1.4: 取得必要資訊
1. 進入剛建立的 Channel
2. 在「Basic settings」頁面記錄：
   - **Channel ID**
   - **Channel secret**
3. 在「Messaging API」頁面：
   - 點擊「Issue」產生 **Channel access token**
   - 記錄此 Token（僅顯示一次）

### 2. Google Sheets 資料庫設定

#### 步驟 2.1: 建立知識庫 Sheet
1. 開啟 [Google Sheets](https://sheets.google.com/)
2. 建立新試算表，命名為「雲林光合菌協會知識庫」
3. 設定第一列標題：
   ```
   A1: 關鍵字
   B1: 回答
   C1: 分類
   ```
4. 填入範例資料：
   ```
   A2: 光合菌    B2: 光合菌是一群能夠進行光合作用的細菌...    C2: 基礎知識
   A3: 應用      B3: 光合菌可應用於農業、水產、環保等領域...  C3: 應用資訊
   A4: 產品      B4: 我們提供多種光合菌產品...              C4: 產品資訊
   ```
5. 記錄此 Sheet 的 ID（URL 中的長字串）

#### 步驟 2.2: 建立對話記錄 Sheet
1. 建立另一個新試算表，命名為「雲林光合菌協會對話記錄」
2. 建立三個工作表：
   - **Sheet1** (對話記錄): 時間戳記 | 用戶ID | 事件類型 | 訊息內容 | 完整事件
   - **Users** (用戶記錄): 時間戳記 | 用戶ID | 動作 | 備註
   - **Errors** (錯誤記錄): 時間戳記 | 函數名稱 | 錯誤訊息 | 錯誤堆疊 | 版本
3. 記錄此 Sheet 的 ID

### 3. Google Apps Script 專案設定

#### 步驟 3.1: 建立 GAS 專案
1. 前往 [Google Apps Script](https://script.google.com/)
2. 點擊「新專案」
3. 將專案重新命名為「雲林光合菌協會Line BOT」

#### 步驟 3.2: 匯入程式碼
1. 刪除預設的 `Code.gs` 內容
2. 複製本專案的 `code.gs` 完整內容並貼上
3. 點擊「檔案」→「專案屬性」→「指令碼資訊清單」
4. 刪除現有內容，複製 `appsscript.json` 內容並貼上
5. 儲存專案

#### 步驟 3.3: 設定專案屬性
1. 點擊「專案設定」（齒輪圖示）
2. 在「指令碼屬性」區段點擊「新增指令碼屬性」
3. 依序新增以下屬性：

| 屬性 | 值 | 說明 |
|------|----|----- |
| `CHANNEL_ACCESS_TOKEN` | [從 Line Console 取得] | Line Channel Access Token |
| `CHANNEL_SECRET` | [從 Line Console 取得] | Line Channel Secret |
| `KNOWLEDGE_BASE_SHEET_ID` | `1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk` | 知識庫 Google Sheets ID |
| `CONVERSATION_LOG_SHEET_ID` | `1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk` | 對話記錄 Google Sheets ID |

## 部署步驟

### 步驟 1: 測試系統功能
1. 在 Google Apps Script 編輯器中
2. 選擇函數 `testSystem`
3. 點擊「執行」
4. 檢查執行日誌，確認所有測試通過

### 步驟 2: 部署為 Web App
1. 點擊「部署」→「新增部署作業」
2. 選擇類型：「網頁應用程式」
3. 設定：
   - **說明**: `雲林光合菌協會Line BOT v1.0.0`
   - **執行身分**: 我
   - **具有存取權的使用者**: 任何人
4. 點擊「部署」
5. 授權必要權限
6. 複製 **Web 應用程式 URL**

### 步驟 3: 設定 Line Webhook
1. 回到 Line Developers Console
2. 進入您的 Messaging API Channel
3. 在「Messaging API」頁面：
   - **Webhook URL**: 貼上剛複製的 Web App URL
   - **Use webhook**: 啟用
   - **Auto-reply messages**: 停用
   - **Greeting messages**: 可選擇啟用
4. 點擊「Verify」驗證 Webhook
5. 確認顯示「Success」

### 步驟 4: 測試 Line BOT
1. 在 Line Developers Console 取得 BOT 的 QR Code
2. 使用手機 Line App 掃描 QR Code 加為好友
3. 傳送測試訊息：
   - `光合菌是什麼？`
   - `/help`
   - `產品資訊`
4. 確認 BOT 正常回應

## 部署後設定

### 1. 自訂 BOT 資訊
1. 在 Line Developers Console 設定：
   - **BOT 名稱**: 雲林光合菌協會客服
   - **BOT 描述**: 提供光合菌專業諮詢服務
   - **BOT 頭像**: 上傳協會 Logo
   - **背景圖片**: 可選擇上傳相關圖片

### 2. 豐富回應設定
1. 在「Messaging API」→「Rich menu」設定選單
2. 建議選單項目：
   - 光合菌知識
   - 產品資訊
   - 聯絡我們
   - 關於協會

### 3. 知識庫擴充
1. 開啟知識庫 Google Sheets
2. 根據常見問題新增更多條目
3. 定期更新和維護內容

## 監控與維護

### 1. 日常監控
- 檢查對話記錄 Sheet，了解用戶互動情況
- 查看錯誤記錄 Sheet，及時處理系統問題
- 監控 Google Apps Script 執行配額

### 2. 定期維護
- 每月更新知識庫內容
- 每季檢視系統效能
- 根據用戶反饋優化回應內容

### 3. 版本更新
1. 修改 `code.gs` 中的版本號
2. 更新文件註解中的版本歷史
3. 部署新版本
4. 測試功能正常運作

## 故障排除

### 常見問題

#### 1. Webhook 驗證失敗
**症狀**: Line Console 顯示 Webhook 驗證失敗
**解決方案**:
- 確認 Web App 部署狀態為「作用中」
- 檢查 `CHANNEL_SECRET` 設定是否正確
- 重新部署 Google Apps Script

#### 2. BOT 無回應
**症狀**: 傳送訊息給 BOT 但無任何回應
**解決方案**:
- 檢查 Google Apps Script 執行日誌
- 確認 `CHANNEL_ACCESS_TOKEN` 設定正確
- 驗證 Google Sheets 權限設定

#### 3. 知識庫查詢失敗
**症狀**: BOT 回應但無法查詢知識庫內容
**解決方案**:
- 確認 `KNOWLEDGE_BASE_SHEET_ID` 正確
- 檢查 Google Sheets 分享權限
- 驗證知識庫格式正確

### 聯絡支援
如遇到無法解決的問題，請聯繫：
- 技術支援：<EMAIL>
- 系統管理：<EMAIL>

---

部署完成後，您的雲林光合菌協會Line BOT客服系統即可開始為用戶提供24小時專業服務！
