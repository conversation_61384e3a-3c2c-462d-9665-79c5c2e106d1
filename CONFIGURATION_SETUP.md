# 雲林光合菌協會 Line BOT 配置設定指南

## 您的專案資訊

根據您提供的資訊，以下是您的專案配置：

### 📋 專案ID資訊
- **Google Apps Script 專案ID**: `1mWN5lA7Nu0m9Do04N12EQ1ECCj75RWQp6nViTHosM27Es8Kt310bcRFI`
- **Google Drive ID**: `1kQKlBvCAIMxyeUZctCc69aG75eK6bp0E`
- **Google Sheets ID**: `1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk`

## 🚀 快速配置步驟

### 步驟 1: 存取您的 Google Apps Script 專案

1. 開啟瀏覽器，前往：
   ```
   https://script.google.com/d/1mWN5lA7Nu0m9Do04N12EQ1ECCj75RWQp6nViTHosM27Es8Kt310bcRFI/edit
   ```

2. 如果專案已存在，您會看到現有的程式碼
3. 如果專案是空的，請複製本專案的 `code.gs` 內容

### 步驟 2: 設定 Google Sheets 資料庫

1. 開啟您的 Google Sheets：
   ```
   https://docs.google.com/spreadsheets/d/1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk/edit
   ```

2. 設定知識庫工作表：
   - 確保第一個工作表名稱為 "Sheet1" 或 "知識庫"
   - 設定標題列：A1=關鍵字, B1=回答, C1=分類
   - 複製 `knowledge_base.csv` 的內容到此工作表

3. 建立對話記錄工作表：
   - 新增工作表，命名為 "對話記錄"
   - 設定標題列：A1=時間戳記, B1=用戶ID, C1=事件類型, D1=訊息內容, E1=完整事件

4. 建立用戶記錄工作表：
   - 新增工作表，命名為 "Users"
   - 設定標題列：A1=時間戳記, B1=用戶ID, C1=動作, D1=備註

5. 建立錯誤記錄工作表：
   - 新增工作表，命名為 "Errors"
   - 設定標題列：A1=時間戳記, B1=函數名稱, C1=錯誤訊息, D1=錯誤堆疊, E1=版本

### 步驟 3: 配置 Google Apps Script 屬性

1. 在 Google Apps Script 編輯器中，點擊左側的「專案設定」（齒輪圖示）

2. 滾動到「指令碼屬性」區段

3. 點擊「新增指令碼屬性」，依序新增以下屬性：

#### 必要屬性設定

| 屬性名稱 | 屬性值 | 說明 |
|---------|-------|------|
| `KNOWLEDGE_BASE_SHEET_ID` | `1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk` | 知識庫Google Sheets ID |
| `CONVERSATION_LOG_SHEET_ID` | `1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk` | 對話記錄Google Sheets ID |
| `CHANNEL_ACCESS_TOKEN` | `[待填入]` | Line Channel Access Token |
| `CHANNEL_SECRET` | `[待填入]` | Line Channel Secret |

### 步驟 4: 取得 Line Channel 資訊

#### 4.1 建立 Line Developer 帳號
1. 前往 [Line Developers Console](https://developers.line.biz/)
2. 使用 Line 帳號登入
3. 建立新的 Provider（如果還沒有）

#### 4.2 建立 Messaging API Channel
1. 在 Provider 中點擊「Create a Messaging API channel」
2. 填寫 Channel 資訊：
   - **Channel name**: `雲林光合菌協會客服BOT`
   - **Channel description**: `提供光合菌相關專業諮詢服務`
   - **Category**: `Science & Technology`
   - **Subcategory**: `Biotechnology`

#### 4.3 取得必要 Token
1. 在 Channel 的「Basic settings」頁面找到：
   - **Channel secret** - 複製此值

2. 在「Messaging API」頁面：
   - 點擊「Issue」按鈕產生 **Channel access token**
   - 複製此 token（只會顯示一次）

#### 4.4 更新 Google Apps Script 屬性
回到 Google Apps Script，更新以下屬性：
- `CHANNEL_ACCESS_TOKEN`: 貼上剛才取得的 Channel access token
- `CHANNEL_SECRET`: 貼上剛才取得的 Channel secret

### 步驟 5: 部署 Web App

1. 在 Google Apps Script 編輯器中，點擊右上角的「部署」→「新增部署作業」

2. 選擇類型：「網頁應用程式」

3. 設定部署參數：
   - **說明**: `雲林光合菌協會Line BOT v1.0.0`
   - **執行身分**: 我
   - **具有存取權的使用者**: 任何人

4. 點擊「部署」

5. 授權必要權限（如果系統要求）

6. 複製產生的 **Web 應用程式 URL**

### 步驟 6: 設定 Line Webhook

1. 回到 Line Developers Console

2. 在您的 Messaging API Channel 中，進入「Messaging API」頁面

3. 設定 Webhook：
   - **Webhook URL**: 貼上步驟5取得的 Web App URL
   - **Use webhook**: 啟用（打開開關）

4. 點擊「Verify」驗證 Webhook

5. 確認顯示「Success」

6. 其他設定：
   - **Auto-reply messages**: 停用
   - **Greeting messages**: 可選擇啟用

### 步驟 7: 測試系統

#### 7.1 系統功能測試
1. 在 Google Apps Script 編輯器中
2. 選擇函數 `testSystem`
3. 點擊「執行」
4. 檢查執行日誌，確認測試通過

#### 7.2 Line BOT 測試
1. 在 Line Developers Console 取得 BOT 的 QR Code
2. 使用手機 Line App 掃描加為好友
3. 測試對話：
   - 發送：`光合菌是什麼？`
   - 發送：`/help`
   - 發送：`產品資訊`

## 🔧 PowerShell 指令協助

如果您需要使用 PowerShell 檢查檔案或進行操作：

### 檢查專案檔案
```powershell
# 列出當前目錄檔案
Get-ChildItem -Path "C:\Users\<USER>\OneDrive - MooMoo\光合菌協會\雲林光合菌協會Line BOT"

# 檢查特定檔案內容
Get-Content -Path ".\code.gs" -TotalCount 20
```

### 備份重要檔案
```powershell
# 建立備份目錄
New-Item -ItemType Directory -Path ".\backup" -Force

# 複製重要檔案到備份目錄
Copy-Item -Path ".\code.gs" -Destination ".\backup\code_backup_$(Get-Date -Format 'yyyyMMdd').gs"
Copy-Item -Path ".\knowledge_base.csv" -Destination ".\backup\"
```

## 📊 Google Sheets 範本設定

### 知識庫工作表範例
```
A1: 關鍵字    B1: 回答    C1: 分類
A2: 光合菌    B2: 光合菌是一群能夠進行光合作用的細菌...    C2: 基礎知識
A3: 應用      B3: 光合菌可應用於農業、水產、環保等領域...  C3: 應用資訊
```

### 對話記錄工作表範例
```
A1: 時間戳記    B1: 用戶ID    C1: 事件類型    D1: 訊息內容    E1: 完整事件
```

## ⚠️ 重要注意事項

1. **權限設定**：
   - 確保 Google Sheets 的分享權限設為「知道連結的使用者」或「任何人」
   - Google Apps Script 需要有存取 Sheets 的權限

2. **安全性**：
   - Channel Secret 和 Access Token 是敏感資訊，請妥善保管
   - 不要在程式碼中直接寫入這些資訊

3. **測試建議**：
   - 先在測試環境完成所有設定
   - 確認所有功能正常後再正式啟用

## 🆘 故障排除

### 常見問題

#### 1. Webhook 驗證失敗
- 檢查 Web App 是否正確部署
- 確認 CHANNEL_SECRET 設定正確
- 重新部署 Google Apps Script

#### 2. BOT 無回應
- 檢查 CHANNEL_ACCESS_TOKEN 是否正確
- 確認 Google Sheets 權限設定
- 查看 Google Apps Script 執行日誌

#### 3. 知識庫無法查詢
- 確認 KNOWLEDGE_BASE_SHEET_ID 正確
- 檢查 Google Sheets 格式是否正確
- 驗證工作表名稱和欄位設定

## 📞 支援聯絡

如果在設定過程中遇到問題：
- 技術支援：<EMAIL>
- 緊急協助：請提供錯誤訊息和操作步驟

---

按照以上步驟完成設定後，您的雲林光合菌協會Line BOT就可以開始為用戶提供專業服務了！
