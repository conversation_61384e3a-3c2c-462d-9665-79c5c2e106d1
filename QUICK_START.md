# 🚀 雲林光合菌協會 Line BOT 快速開始指南

## 📋 專案狀態

✅ **所有檔案已準備完成**
- 主程式：`code.gs` (668行)
- 配置：`appsscript.json`
- Clasp配置：`.clasp.json`
- 部署腳本：`deploy.ps1`

## 🎯 您的專案資訊

- **GAS專案ID**: `1mWN5lA7Nu0m9Do04N12EQ1ECCj75RWQp6nViTHosM27Es8Kt310bcRFI`
- **Sheets ID**: `1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk`

## ⚡ 快速部署步驟

### 步驟 1: 安裝 Clasp (如果尚未安裝)

```powershell
# 安裝 Node.js (如果尚未安裝)
# 前往 https://nodejs.org/ 下載並安裝

# 安裝 Google Apps Script CLI
npm install -g @google/clasp

# 驗證安裝
clasp --version
```

### 步驟 2: 設定 Google Apps Script API

1. 前往 [Google Apps Script 設定](https://script.google.com/home/<USER>
2. 開啟「Google Apps Script API」開關

### 步驟 3: 執行自動化設定檢查

```powershell
# 切換到專案目錄
cd "C:\Users\<USER>\OneDrive - MooMoo\光合菌協會\雲林光合菌協會Line BOT"

# 執行設定檢查
.\deploy.ps1 -Action setup
```

### 步驟 4: 登入 Google 帳號 (如果需要)

```powershell
# 登入您的 Google 帳號
clasp login
```

### 步驟 5: 執行完整部署

```powershell
# 執行完整部署流程
.\deploy.ps1 -Action deploy -Message "雲林光合菌協會Line BOT初始部署"
```

## 🔧 常用指令

### 部署相關指令

```powershell
# 檢查系統狀態
.\deploy.ps1 -Action check

# 僅推送檔案到 GAS
.\deploy.ps1 -Action push

# 完整部署 (推送 + 版本 + 部署)
.\deploy.ps1 -Action deploy

# 強制推送 (覆蓋遠端檔案)
.\deploy.ps1 -Action push -Force

# 查看專案資訊
.\deploy.ps1 -Action info

# 開啟 GAS 編輯器
.\deploy.ps1 -Action open
```

### 原生 Clasp 指令

```powershell
# 檢查狀態
clasp status

# 推送檔案
clasp push

# 拉取檔案
clasp pull

# 開啟編輯器
clasp open

# 查看部署
clasp deployments

# 建立部署
clasp deploy --description "版本說明"
```

## 📊 部署後設定

### 1. 設定 Google Apps Script 屬性

部署完成後，在 GAS 編輯器中設定以下屬性：

```
專案設定 → 指令碼屬性 → 新增指令碼屬性
```

| 屬性名稱 | 屬性值 |
|---------|-------|
| `KNOWLEDGE_BASE_SHEET_ID` | `1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk` |
| `CONVERSATION_LOG_SHEET_ID` | `1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk` |
| `CHANNEL_ACCESS_TOKEN` | [從Line Console取得] |
| `CHANNEL_SECRET` | [從Line Console取得] |

### 2. 設定 Google Sheets

開啟您的 Google Sheets：
https://docs.google.com/spreadsheets/d/1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk/edit

建立以下工作表：
- **Sheet1** (知識庫): 關鍵字 | 回答 | 分類
- **對話記錄**: 時間戳記 | 用戶ID | 事件類型 | 訊息內容 | 完整事件
- **Users**: 時間戳記 | 用戶ID | 動作 | 備註
- **Errors**: 時間戳記 | 函數名稱 | 錯誤訊息 | 錯誤堆疊 | 版本

### 3. 部署 Web App

在 GAS 編輯器中：
1. 點擊「部署」→「新增部署作業」
2. 類型：網頁應用程式
3. 執行身分：我
4. 存取權：任何人
5. 複製 Web App URL

### 4. 設定 Line Webhook

1. 前往 [Line Developers Console](https://developers.line.biz/)
2. 建立 Messaging API Channel
3. 設定 Webhook URL (使用步驟3的URL)
4. 啟用 Webhook

## 🧪 測試系統

### 系統功能測試

在 GAS 編輯器中執行：

```javascript
// 測試系統初始化
initializeSystem()

// 測試系統功能
testSystem()
```

### Line BOT 測試

1. 取得 BOT QR Code
2. 加為好友
3. 測試對話：
   - `光合菌是什麼？`
   - `/help`
   - `產品資訊`
   - `聯絡方式`

## 📁 專案檔案說明

### 核心檔案
- `code.gs` - 主程式檔案
- `appsscript.json` - GAS 配置
- `.clasp.json` - Clasp 專案配置
- `.claspignore` - 忽略檔案清單

### 部署工具
- `deploy.ps1` - 自動化部署腳本
- `CLASP_DEPLOYMENT.md` - Clasp 詳細指南

### 文件資料
- `README.md` - 專案說明
- `CONFIGURATION_SETUP.md` - 配置指南
- `TEST_GUIDE.md` - 測試指南
- `YOUR_PROJECT_INFO.txt` - 專案資訊總覽

## 🆘 故障排除

### 常見問題

#### 1. Clasp 未安裝
```powershell
npm install -g @google/clasp
```

#### 2. 未登入 Google 帳號
```powershell
clasp login
```

#### 3. Apps Script API 未啟用
前往 https://script.google.com/home/<USER>

#### 4. 推送失敗
```powershell
.\deploy.ps1 -Action push -Force
```

#### 5. 權限問題
確認 Google 帳號有 GAS 專案的編輯權限

## 🔗 重要連結

- **GAS 專案**: https://script.google.com/d/1mWN5lA7Nu0m9Do04N12EQ1ECCj75RWQp6nViTHosM27Es8Kt310bcRFI/edit
- **Google Sheets**: https://docs.google.com/spreadsheets/d/1tm-FiIqTJ4YtHycFzFUob11bRFsIRRn3OY29bdACOPk/edit
- **Line Developers**: https://developers.line.biz/
- **Apps Script API**: https://script.google.com/home/<USER>

## 📞 支援

如需協助：
- 技術支援：<EMAIL>
- 查看文件：`CLASP_DEPLOYMENT.md`
- 故障排除：`TEST_GUIDE.md`

---

🎉 **準備就緒！**
您的雲林光合菌協會 Line BOT 已準備好部署到 Google Apps Script！

執行 `.\deploy.ps1 -Action deploy` 開始部署吧！
