# ✅ 功能驗證檢查清單

## 🚀 v1.3.0 功能驗證

**更新日期**: 2025-01-24  
**推送狀態**: ✅ 成功推送到 Google Apps Script

## 📋 立即驗證步驟

### 步驟 1: 確認函數存在

在 Google Apps Script 編輯器中，檢查以下函數是否存在：

#### 🧠 Gemini AI 核心函數
- [ ] `analyzeUserIntent(userText)` - 意圖分析
- [ ] `generateGeminiResponse(userText, intentData)` - 回應生成
- [ ] `showTyping(userId)` - 輸入動畫

#### 🧪 測試函數
- [ ] `checkGeminiConfig()` - 檢查Gemini配置
- [ ] `testGeminiIntegration()` - 測試AI整合
- [ ] `fullAITest()` - 完整AI測試
- [ ] `testShowTyping()` - 測試輸入動畫

#### 📊 處理函數
- [ ] `processTextMessageWithAI(text, userId, replyToken)` - AI文字處理
- [ ] `processTextMessageTraditional(text, userId)` - 傳統處理
- [ ] `getAboutMessage()` - 關於協會訊息

### 步驟 2: 執行基本配置檢查

```javascript
// 在 GAS 編輯器中執行
checkGeminiConfig()
```

**預期結果**:
```
=== Gemini API 配置檢查 ===
Gemini API Key: 未設定  // 或 "已設定"
❌ 請在專案設定中新增 GEMINI_API_KEY 屬性
```

### 步驟 3: 設定 Gemini API Key

1. **取得 API Key**:
   - 前往 https://aistudio.google.com/
   - 建立新的 API Key

2. **在 GAS 中設定**:
   - 點擊「專案設定」（齒輪圖示）
   - 滾動到「指令碼屬性」
   - 點擊「新增指令碼屬性」
   - 屬性名稱: `GEMINI_API_KEY`
   - 屬性值: `[您的 Gemini API Key]`

### 步驟 4: 重新檢查配置

```javascript
// 再次執行配置檢查
checkGeminiConfig()
```

**預期結果**:
```
=== Gemini API 配置檢查 ===
Gemini API Key: 已設定
✅ Gemini API Key 已正確設定
API URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent
Max Output Tokens: 8192
```

### 步驟 5: 測試 AI 整合

```javascript
// 執行 AI 整合測試
testGeminiIntegration()
```

**預期結果**:
```
=== Gemini AI 整合測試 ===
✅ Gemini API Key 已正確設定

--- 意圖分析測試 ---
測試文字: "光合菌是什麼？"
✅ 意圖: KNOWLEDGE_INQUIRY
---
測試文字: "產品資訊"
✅ 意圖: PRODUCT_INQUIRY
---
...

--- 回應生成測試 ---
✅ 回應生成成功
生成的回應: [AI生成的專業回應]

=== 測試完成 ===
```

### 步驟 6: 執行完整測試

```javascript
// 執行完整功能測試
fullAITest()
```

**預期結果**:
```
=== 完整 AI 功能測試 ===
1. 檢查配置...
配置檢查: 通過

2. 意圖分析測試...
"光合菌是什麼？" → KNOWLEDGE_INQUIRY (預期: KNOWLEDGE_INQUIRY) ✅
"產品價格" → PRODUCT_INQUIRY (預期: PRODUCT_INQUIRY) ✅
...
意圖分析通過率: 4/4 (100%)

3. 回應生成測試...
"光合菌的用途" (APPLICATION_INQUIRY) → ✅ 成功
...
回應生成通過率: 3/3 (100%)

=== 測試總結 ===
整體評估: ✅ 通過
```

### 步驟 7: 測試 Line BOT 功能

發送以下測試訊息到您的 Line BOT：

#### 🧠 AI 功能測試
```
1. 光合菌是什麼？
2. 光合菌有什麼用途？
3. 你們有什麼產品？
4. 如何聯繫協會？
5. 介紹一下協會
```

#### ⚡ 用戶體驗測試
```
6. 你好
7. 謝謝你的幫助
8. 今天天氣如何？
```

#### 🔧 系統指令測試
```
9. /help
10. /about
11. /contact
12. /version
```

### 步驟 8: 檢查執行日誌

在 GAS 編輯器的「執行」頁面，查看是否有以下日誌：

#### ✅ 正常 AI 流程
```
doPost called - processing webhook request
handleMessage called with event: {...}
Analyzing user intent with Gemini...
Detected intent: KNOWLEDGE_INQUIRY
Generating Gemini response for intent: KNOWLEDGE_INQUIRY
Generated Gemini response: [AI回應內容]
Sending reply message: "[回應內容]"
Message sent successfully
```

#### ⚠️ 備援機制啟動
```
Gemini API key not configured
AI response failed, falling back to traditional method
Using traditional processing
Found knowledge base response
```

#### ❌ 錯誤情況
```
Gemini API error: 401 - Invalid API key
Failed to parse Gemini JSON response
All methods failed, using default response
```

## 🔧 故障排除

### 問題 1: 找不到測試函數

**症狀**: 在 GAS 編輯器中找不到 `checkGeminiConfig` 等函數

**解決方案**:
1. 確認程式碼已正確推送：`clasp push`
2. 重新整理 GAS 編輯器頁面
3. 檢查函數下拉選單中是否出現新函數

### 問題 2: API Key 設定後仍顯示未設定

**症狀**: 執行 `checkGeminiConfig()` 仍顯示「未設定」

**解決方案**:
1. 確認屬性名稱拼寫正確：`GEMINI_API_KEY`
2. 確認 API Key 沒有多餘的空格
3. 重新執行函數

### 問題 3: Gemini API 錯誤

**症狀**: 出現 401、403 等 API 錯誤

**解決方案**:
1. 確認 API Key 有效且未過期
2. 檢查 Google AI Studio 中的配額使用情況
3. 確認 API Key 有正確的權限

### 問題 4: AI 回應品質不佳

**症狀**: AI 回應不相關或品質差

**解決方案**:
1. 檢查意圖分析是否正確
2. 調整提示詞模板
3. 增加更多訓練範例

## 📊 效能基準

### 正常運行指標
- **意圖分析準確率**: > 90%
- **回應生成成功率**: > 95%
- **總回應時間**: < 5秒
- **備援機制啟動率**: < 5%

### API 使用量監控
- **每日 API 調用**: 建議 < 1000次
- **每月配額使用**: 建議 < 80%
- **錯誤率**: < 1%

## 📞 技術支援

如果驗證過程中遇到問題：

1. **檢查推送狀態**: 執行 `clasp status` 確認檔案同步
2. **查看完整日誌**: 在 GAS 編輯器中檢查詳細執行日誌
3. **執行診斷**: 運行 `fullAITest()` 獲取完整診斷報告
4. **聯繫支援**: <EMAIL>

## ✅ 驗證完成確認

完成所有驗證步驟後，請確認：

- [ ] 所有測試函數都能正常執行
- [ ] Gemini API Key 已正確設定
- [ ] AI 功能測試通過率 > 80%
- [ ] Line BOT 能正常回應各種訊息
- [ ] 執行日誌顯示正常的 AI 處理流程
- [ ] showTyping 動畫功能正常（在實際對話中測試）

**🎉 驗證完成！您的 AI 客服系統已準備就緒！**

---

**重要提醒**: 
- Gemini API 有使用配額限制，請適度使用
- 定期檢查 API 使用量和費用
- 建議設定適當的使用警告和限制
